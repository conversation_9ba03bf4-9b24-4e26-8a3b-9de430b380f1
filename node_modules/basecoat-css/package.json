{"name": "basecoat-css", "version": "0.2.8", "description": "Tailwind CSS for Basecoat components", "author": {"name": "hun<PERSON>us", "url": "https://x.com/hunvreus"}, "license": "MIT", "type": "module", "main": "dist/basecoat.css", "style": "dist/basecoat.css", "files": ["dist/"], "keywords": ["components", "component library", "component system", "ui", "ui kit", "shadcn", "shadcn/ui", "tailwind", "tailwindcss", "css", "html", "jinja", "nunjucks", "alpinej<PERSON>"], "repository": {"type": "git", "url": "git+https://github.com/hunvreus/basecoat.git", "directory": "packages/css"}, "bugs": {"url": "https://github.com/hunvreus/basecoat/issues"}, "homepage": "https://basecoatui.com/installation#install-npm", "exports": {".": "./dist/basecoat.css", "./css": "./dist/basecoat.css", "./all": "./dist/js/all.js", "./all.min": "./dist/js/all.min.js", "./dropdown-menu": "./dist/js/dropdown-menu.js", "./dropdown-menu.min": "./dist/js/dropdown-menu.min.js", "./popover": "./dist/js/popover.js", "./popover.min": "./dist/js/popover.min.js", "./select": "./dist/js/select.js", "./select.min": "./dist/js/select.min.js", "./sidebar": "./dist/js/sidebar.js", "./sidebar.min": "./dist/js/sidebar.min.js", "./tabs": "./dist/js/tabs.js", "./tabs.min": "./dist/js/tabs.min.js", "./toast": "./dist/js/toast.js", "./toast.min": "./dist/js/toast.min.js", "./package.json": "./package.json"}}