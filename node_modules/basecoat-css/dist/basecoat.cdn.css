/*! tailwindcss v4.1.4 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-xs: 20rem;
    --container-lg: 32rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --tracking-tight: -0.025em;
    --leading-relaxed: 1.625;
    --radius-xs: 0.125rem;
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities;
:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --sidebar-width: 16rem;
  --sidebar-mobile-width: 18rem;
  --scrollbar-track: transparent;
  --scrollbar-thumb: rgba(0, 0, 0, 0.3);
  --scrollbar-width: 6px;
  --scrollbar-radius: 6px;
  --chevron-down-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="oklch(0.556 0 0)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down-icon lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>');
  --chevron-down-icon-50: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="oklch(0.556 0 0 / 0.5)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down-icon lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>');
  --check-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="oklch(0.556 0 0)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>');
}
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.269 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.371 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.439 0 0);
  --scrollbar-thumb: rgba(255, 255, 255, 0.3);
  --chevron-down-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="oklch(0.708 0 0)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down-icon lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>');
  --chevron-down-icon-50: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="oklch(0.708 0 0 / 0.5)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down-icon lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>');
  --check-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="oklch(0.708 0 0)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>');
  color-scheme: dark;
}
@layer base {
  * {
    border-color: var(--color-border);
    outline-color: var(--color-ring);
    @supports (color: color-mix(in lab, red, red)) {
      outline-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
    }
  }
  html {
    scroll-behavior: smooth;
  }
  body {
    overscroll-behavior: none;
    background-color: var(--color-background);
    color: var(--color-foreground);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
    &::-webkit-scrollbar {
      width: var(--scrollbar-width);
    }
    &::-webkit-scrollbar-track {
      background: var(--scrollbar-track);
    }
    &::-webkit-scrollbar-thumb {
      background: var(--scrollbar-thumb);
      border-radius: var(--scrollbar-radius);
    }
  }
  [x-cloak] {
    display: none !important;
  }
}
@layer components {
  .alert, .alert-destructive {
    position: relative;
    display: grid;
    width: 100%;
    grid-template-columns: 0 1fr;
    align-items: flex-start;
    row-gap: calc(var(--spacing) * 0.5);
    border-radius: var(--radius-lg);
    border-style: var(--tw-border-style);
    border-width: 1px;
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 3);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    &:has(>svg) {
      grid-template-columns: calc(var(--spacing) * 4) 1fr;
    }
    &:has(>svg) {
      column-gap: calc(var(--spacing) * 3);
    }
    &>svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
    &>svg {
      --tw-translate-y: calc(var(--spacing) * 0.5);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    &>svg {
      color: currentcolor;
    }
    h2 {
      grid-column-start: 2;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      min-height: calc(var(--spacing) * 4);
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
      --tw-tracking: var(--tracking-tight);
      letter-spacing: var(--tracking-tight);
    }
    section {
      grid-column-start: 2;
      display: grid;
      justify-items: start;
      gap: calc(var(--spacing) * 1);
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
      color: var(--color-muted-foreground);
      & p {
        --tw-leading: var(--leading-relaxed);
        line-height: var(--leading-relaxed);
      }
      ul {
        list-style-position: inside;
        list-style-type: disc;
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
      }
    }
  }
  .alert {
    background-color: var(--color-card);
    color: var(--color-card-foreground);
  }
  .alert-destructive {
    background-color: var(--color-card);
    color: var(--color-destructive);
    &>svg {
      color: currentcolor;
    }
    section {
      color: var(--color-destructive);
    }
  }
}
@layer components {
  .btn, .btn-primary, .btn-secondary, .btn-outline, .btn-ghost, .btn-link, .btn-destructive, .btn-sm, .btn-sm-primary, .btn-sm-secondary, .btn-sm-outline, .btn-sm-ghost, .btn-sm-link, .btn-sm-destructive, .btn-lg, .btn-lg-primary, .btn-lg-secondary, .btn-lg-outline, .btn-lg-ghost, .btn-lg-link, .btn-lg-destructive, .btn-icon, .btn-icon-primary, .btn-icon-secondary, .btn-icon-outline, .btn-icon-ghost, .btn-icon-link, .btn-icon-destructive, .btn-sm-icon, .btn-sm-icon-primary, .btn-sm-icon-secondary, .btn-sm-icon-outline, .btn-sm-icon-ghost, .btn-sm-icon-link, .btn-sm-icon-destructive, .btn-lg-icon, .btn-lg-icon-primary, .btn-lg-icon-secondary, .btn-lg-icon-outline, .btn-lg-icon-ghost, .btn-lg-icon-link, .btn-lg-icon-destructive {
    display: inline-flex;
    flex-shrink: 0;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-outline-style: none;
    outline-style: none;
    &:focus-visible {
      border-color: var(--color-ring);
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--color-ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
      }
    }
    &:disabled {
      pointer-events: none;
    }
    &:disabled {
      opacity: 50%;
    }
    &[aria-invalid="true"] {
      border-color: var(--color-destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--color-destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
      }
    }
    &:is(.dark *) {
      &[aria-invalid="true"] {
        --tw-ring-color: var(--color-destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
        }
      }
    }
    & svg {
      pointer-events: none;
    }
    & svg {
      flex-shrink: 0;
    }
    & svg:not([class*='size-']) {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
  .btn, .btn-primary, .btn-secondary, .btn-outline, .btn-ghost, .btn-link, .btn-destructive {
    height: calc(var(--spacing) * 9);
    gap: calc(var(--spacing) * 2);
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 2);
    &:has(>svg) {
      padding-inline: calc(var(--spacing) * 3);
    }
  }
  .btn-icon, .btn-icon-primary, .btn-icon-secondary, .btn-icon-outline, .btn-icon-ghost, .btn-icon-link, .btn-icon-destructive {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }
  .btn-sm, .btn-sm-primary, .btn-sm-secondary, .btn-sm-outline, .btn-sm-ghost, .btn-sm-link, .btn-sm-destructive {
    height: calc(var(--spacing) * 8);
    gap: calc(var(--spacing) * 1.5);
    padding-inline: calc(var(--spacing) * 3);
    &:has(>svg) {
      padding-inline: calc(var(--spacing) * 2.5);
    }
  }
  .btn-sm-icon, .btn-sm-icon-primary, .btn-sm-icon-secondary, .btn-sm-icon-outline, .btn-sm-icon-ghost, .btn-sm-icon-link, .btn-sm-icon-destructive {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }
  .btn-lg, .btn-lg-primary, .btn-lg-secondary, .btn-lg-outline, .btn-lg-ghost, .btn-lg-link, .btn-lg-destructive {
    height: calc(var(--spacing) * 10);
    gap: calc(var(--spacing) * 2);
    padding-inline: calc(var(--spacing) * 6);
    &:has(>svg) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .btn-lg-icon, .btn-lg-icon-primary, .btn-lg-icon-secondary, .btn-lg-icon-outline, .btn-lg-icon-ghost, .btn-lg-icon-link, .btn-lg-icon-destructive {
    width: calc(var(--spacing) * 10);
    height: calc(var(--spacing) * 10);
  }
  .btn, .btn-primary, .btn-sm, .btn-sm-primary, .btn-lg, .btn-lg-primary, .btn-icon, .btn-icon-primary, .btn-sm-icon, .btn-sm-icon-primary, .btn-lg-icon, .btn-lg-icon-primary {
    background-color: var(--color-primary);
    color: var(--color-primary-foreground);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);
        }
      }
    }
    &[aria-pressed='true'] {
      background-color: var(--color-primary);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);
      }
    }
  }
  .btn-secondary, .btn-sm-secondary, .btn-lg-secondary, .btn-icon-secondary, .btn-sm-icon-secondary, .btn-lg-icon-secondary {
    background-color: var(--color-secondary);
    color: var(--color-secondary-foreground);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    &:hover, &[aria-pressed='true'] {
      background-color: var(--color-secondary);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-secondary) 80%, transparent);
      }
    }
  }
  .btn-outline, .btn-sm-outline, .btn-lg-outline, .btn-icon-outline, .btn-sm-icon-outline, .btn-lg-icon-outline {
    border-style: var(--tw-border-style);
    border-width: 1px;
    background-color: var(--color-background);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    &:is(.dark *) {
      border-color: var(--color-input);
    }
    &:is(.dark *) {
      background-color: var(--color-input);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-input) 30%, transparent);
      }
    }
    &:hover, &[aria-pressed='true'] {
      background-color: var(--color-accent);
      color: var(--color-accent-foreground);
      &:is(.dark *) {
        background-color: var(--color-accent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-accent) 50%, transparent);
        }
      }
    }
  }
  .btn-ghost, .btn-sm-ghost, .btn-lg-ghost, .btn-icon-ghost, .btn-sm-icon-ghost, .btn-lg-icon-ghost {
    &:hover, &[aria-pressed='true'] {
      background-color: var(--color-accent);
      color: var(--color-accent-foreground);
      &:is(.dark *) {
        background-color: var(--color-accent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-accent) 50%, transparent);
        }
      }
    }
  }
  .btn-link, .btn-sm-link, .btn-lg-link, .btn-icon-link, .btn-sm-icon-link, .btn-lg-icon-link {
    color: var(--color-primary);
    text-underline-offset: 4px;
    &:hover, &[aria-pressed='true'] {
      &:hover {
        @media (hover: hover) {
          text-decoration-line: underline;
        }
      }
    }
  }
  .btn-destructive, .btn-sm-destructive, .btn-lg-destructive, .btn-icon-destructive, .btn-sm-icon-destructive, .btn-lg-icon-destructive {
    background-color: var(--color-destructive);
    color: var(--color-white);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    &:focus-visible {
      --tw-ring-color: var(--color-destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
      }
    }
    &:is(.dark *) {
      background-color: var(--color-destructive);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-destructive) 60%, transparent);
      }
    }
    &:is(.dark *) {
      &:focus-visible {
        --tw-ring-color: var(--color-destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
        }
      }
    }
    &:hover, &[aria-pressed='true'] {
      background-color: var(--color-destructive);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-destructive) 90%, transparent);
      }
      &:is(.dark *) {
        background-color: var(--color-destructive);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-destructive) 50%, transparent);
        }
      }
    }
  }
}
@layer components {
  .badge, .badge-primary, .badge-secondary, .badge-destructive, .badge-outline {
    display: inline-flex;
    width: fit-content;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    gap: calc(var(--spacing) * 1);
    overflow: hidden;
    border-radius: var(--radius-md);
    border-style: var(--tw-border-style);
    border-width: 1px;
    padding-inline: calc(var(--spacing) * 2);
    padding-block: calc(var(--spacing) * 0.5);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
    transition-property: color,box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    &:focus-visible {
      border-color: var(--color-ring);
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--color-ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
      }
    }
    &[aria-invalid="true"] {
      border-color: var(--color-destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--color-destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
      }
    }
    &:is(.dark *) {
      &[aria-invalid="true"] {
        --tw-ring-color: var(--color-destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
        }
      }
    }
    &>svg {
      pointer-events: none;
    }
    &>svg {
      width: calc(var(--spacing) * 3);
      height: calc(var(--spacing) * 3);
    }
  }
  .badge, .badge-primary {
    border-color: transparent;
    background-color: var(--color-primary);
    color: var(--color-primary-foreground);
    a& {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-primary);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-primary) 90%, transparent);
          }
        }
      }
    }
  }
  .badge-secondary {
    border-color: transparent;
    background-color: var(--color-secondary);
    color: var(--color-secondary-foreground);
    a& {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-secondary);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-secondary) 90%, transparent);
          }
        }
      }
    }
  }
  .badge-destructive {
    border-color: transparent;
    background-color: var(--color-destructive);
    color: var(--color-white);
    &:focus-visible {
      --tw-ring-color: var(--color-destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
      }
    }
    &:is(.dark *) {
      background-color: var(--color-destructive);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-destructive) 60%, transparent);
      }
    }
    &:is(.dark *) {
      &:focus-visible {
        --tw-ring-color: var(--color-destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
        }
      }
    }
    a& {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-destructive);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-destructive) 90%, transparent);
          }
        }
      }
    }
  }
  .badge-outline {
    color: var(--color-foreground);
    a& {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-accent);
        }
      }
    }
    a& {
      &:hover {
        @media (hover: hover) {
          color: var(--color-accent-foreground);
        }
      }
    }
  }
}
@layer components {
  .card {
    display: flex;
    flex-direction: column;
    gap: calc(var(--spacing) * 6);
    border-radius: var(--radius-xl);
    border-style: var(--tw-border-style);
    border-width: 1px;
    background-color: var(--color-card);
    padding-block: calc(var(--spacing) * 6);
    color: var(--color-card-foreground);
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    > header {
      container-type: inline-size;
      container-name: card-header;
      display: grid;
      grid-auto-rows: min-content;
      grid-template-rows: auto auto;
      align-items: flex-start;
      gap: calc(var(--spacing) * 1.5);
      padding-inline: calc(var(--spacing) * 6);
      &:has(*[data-slot="card-action"]) {
        grid-template-columns: 1fr auto;
      }
      &:is(.border-b) {
        padding-bottom: calc(var(--spacing) * 6);
      }
      h2 {
        --tw-leading: 1;
        line-height: 1;
        --tw-font-weight: var(--font-weight-semibold);
        font-weight: var(--font-weight-semibold);
      }
      p {
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
        color: var(--color-muted-foreground);
      }
    }
    > section {
      padding-inline: calc(var(--spacing) * 6);
    }
    > footer {
      display: flex;
      align-items: center;
      padding-inline: calc(var(--spacing) * 6);
      &:is(.border-t) {
        padding-top: calc(var(--spacing) * 6);
      }
    }
  }
}
@layer components {
  .form input[type='checkbox']:not([role='switch']), .input[type='checkbox']:not([role='switch']) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
    flex-shrink: 0;
    appearance: none;
    border-radius: 4px;
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-input);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-outline-style: none;
    outline-style: none;
    &:checked {
      border-color: var(--color-primary);
    }
    &:checked {
      background-color: var(--color-primary);
    }
    &:focus-visible {
      border-color: var(--color-ring);
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--color-ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
      }
    }
    &:disabled {
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 50%;
    }
    &[aria-invalid="true"] {
      border-color: var(--color-destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--color-destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
      }
    }
    &:is(.dark *) {
      background-color: var(--color-input);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-input) 30%, transparent);
      }
    }
    &:is(.dark *) {
      &:checked {
        background-color: var(--color-primary);
      }
    }
    &:is(.dark *) {
      &[aria-invalid="true"] {
        --tw-ring-color: var(--color-destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
        }
      }
    }
    &:checked:after {
      display: block;
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
      background-color: var(--color-primary-foreground);
      --tw-content: '';
      content: var(--tw-content);
      mask-image: var(--check-icon);
      mask-size: 0.875rem;
      mask-position: center;
      mask-repeat: no-repeat;
    }
  }
}
@layer components {
  details {
    &::details-content {
      block-size: 0;
      display: block;
      opacity: 0%;
      transition-property: all;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      transition-behavior: allow-discrete;
    }
    &[open]::details-content {
      block-size: auto;
      block-size: calc-size(auto, size);
      opacity: 100%;
    }
    summary {
      display: inline-flex;
      cursor: pointer;
      align-items: center;
    }
  }
  details > summary::-webkit-details-marker {
    display: none;
  }
}
@layer components {
  .dialog {
    inset-block: calc(var(--spacing) * 0);
    opacity: 0%;
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    transition-behavior: allow-discrete;
    &:is([open],:popover-open) {
      opacity: 100%;
      &::backdrop {
        opacity: 100%;
      }
      > article {
        --tw-scale-x: 100%;
        --tw-scale-y: 100%;
        --tw-scale-z: 100%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
      @starting-style {
        opacity: 0%;
        &::backdrop {
          opacity: 0%;
        }
        > article {
          --tw-scale-x: 95%;
          --tw-scale-y: 95%;
          --tw-scale-z: 95%;
          scale: var(--tw-scale-x) var(--tw-scale-y);
        }
      }
    }
    &::backdrop {
      background-color: color-mix(in srgb, #000 50%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
      }
      opacity: 0%;
      transition-property: all;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      transition-behavior: allow-discrete;
    }
    > article {
      position: fixed;
      top: 50%;
      left: 50%;
      z-index: 50;
      display: flex;
      max-height: calc(100% - 2rem);
      width: 100%;
      max-width: calc(100% - 2rem);
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
      --tw-translate-y: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
      flex-direction: column;
      gap: calc(var(--spacing) * 4);
      border-radius: var(--radius-lg);
      border-style: var(--tw-border-style);
      border-width: 1px;
      background-color: var(--color-background);
      padding: calc(var(--spacing) * 6);
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      @media (width >= 40rem) {
        max-width: var(--container-lg);
      }
      --tw-scale-x: 95%;
      --tw-scale-y: 95%;
      --tw-scale-z: 95%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
      transition-property: all;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      > header {
        display: flex;
        flex-direction: column;
        gap: calc(var(--spacing) * 2);
        text-align: center;
        @media (width >= 40rem) {
          text-align: left;
        }
        > h2 {
          font-size: var(--text-lg);
          line-height: var(--tw-leading, var(--text-lg--line-height));
          --tw-leading: 1;
          line-height: 1;
          --tw-font-weight: var(--font-weight-semibold);
          font-weight: var(--font-weight-semibold);
        }
        > p {
          font-size: var(--text-sm);
          line-height: var(--tw-leading, var(--text-sm--line-height));
          color: var(--color-muted-foreground);
        }
      }
      > section {
        margin-inline: calc(var(--spacing) * -6);
        flex: 1;
        padding-inline: calc(var(--spacing) * 6);
      }
      > footer {
        display: flex;
        flex-direction: column-reverse;
        gap: calc(var(--spacing) * 2);
        @media (width >= 40rem) {
          flex-direction: row;
        }
        @media (width >= 40rem) {
          justify-content: flex-end;
        }
      }
      > form[method='dialog'] {
        position: absolute;
        top: calc(var(--spacing) * 4);
        right: calc(var(--spacing) * 4);
        > button {
          border-radius: var(--radius-xs);
          opacity: 70%;
          --tw-ring-offset-color: var(--color-background);
          transition-property: opacity;
          transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
          transition-duration: var(--tw-duration, var(--default-transition-duration));
          &:hover {
            @media (hover: hover) {
              opacity: 100%;
            }
          }
          &:focus {
            --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
            box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
          }
          &:focus {
            --tw-ring-color: var(--color-ring);
          }
          &:focus {
            --tw-ring-offset-width: 2px;
            --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
          }
          &:focus {
            --tw-outline-style: none;
            outline-style: none;
            @media (forced-colors: active) {
              outline: 2px solid transparent;
              outline-offset: 2px;
            }
          }
          &:disabled {
            pointer-events: none;
          }
          &[data-state="open"] {
            background-color: var(--color-accent);
          }
          &[data-state="open"] {
            color: var(--color-muted-foreground);
          }
          & svg {
            pointer-events: none;
          }
          & svg {
            flex-shrink: 0;
          }
          & svg:not([class*='size-']) {
            width: calc(var(--spacing) * 4);
            height: calc(var(--spacing) * 4);
          }
        }
      }
    }
  }
}
@layer components {
  .dropdown-menu {
    position: relative;
    display: inline-flex;
    [data-popover] {
      padding: calc(var(--spacing) * 1);
      min-width: anchor-size(width);
      [role='menuitem'], [role='menuitemcheckbox'], [role='menuitemradio'] {
        position: relative;
        display: flex;
        width: 100%;
        cursor: default;
        align-items: center;
        gap: calc(var(--spacing) * 2);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        border-radius: var(--radius-sm);
        padding-inline: calc(var(--spacing) * 2);
        padding-block: calc(var(--spacing) * 1.5);
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
        -webkit-user-select: none;
        user-select: none;
        &:disabled {
          pointer-events: none;
        }
        &:disabled {
          opacity: 50%;
        }
        &[aria-disabled="true"] {
          pointer-events: none;
        }
        &[aria-disabled="true"] {
          opacity: 50%;
        }
        &[aria-hidden="true"] {
          display: none;
        }
        & svg {
          width: calc(var(--spacing) * 4);
          height: calc(var(--spacing) * 4);
        }
        & svg {
          flex-shrink: 0;
        }
        & svg {
          color: var(--color-muted-foreground);
        }
        &:not([aria-disabled='true']) {
          &:focus-visible {
            background-color: var(--color-accent);
          }
          &:focus-visible {
            color: var(--color-accent-foreground);
          }
        }
        &.active {
          background-color: var(--color-accent);
          color: var(--color-accent-foreground);
        }
      }
      [role='menu'] [role='heading'] {
        display: flex;
        padding-inline: calc(var(--spacing) * 2);
        padding-block: calc(var(--spacing) * 1.5);
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
        --tw-font-weight: var(--font-weight-medium);
        font-weight: var(--font-weight-medium);
      }
      [role='separator'] {
        margin-inline: calc(var(--spacing) * -1);
        margin-block: calc(var(--spacing) * 1);
        border-color: var(--color-border);
      }
    }
    &:not([data-dropdown-menu-initialized]) [data-popover] {
      [role='menuitem'], [role='menuitemcheckbox'], [role='menuitemradio'] {
        &:hover {
          @media (hover: hover) {
            background-color: var(--color-accent);
          }
        }
        &:hover {
          @media (hover: hover) {
            color: var(--color-accent-foreground);
          }
        }
      }
    }
  }
}
@layer components {
  .form input[type='text'], .form input[type='email'], .form input[type='password'], .form input[type='number'], .form input[type='file'], .form input[type='tel'], .form input[type='url'], .form input[type='search'], .form input[type='date'], .form input[type='datetime-local'], .form input[type='month'], .form input[type='week'], .form input[type='time'], .input[type='text'], .input[type='email'], .input[type='password'], .input[type='number'], .input[type='file'], .input[type='tel'], .input[type='url'], .input[type='search'], .input[type='date'], .input[type='datetime-local'], .input[type='month'], .input[type='week'], .input[type='time'] {
    display: flex;
    height: calc(var(--spacing) * 9);
    width: 100%;
    min-width: calc(var(--spacing) * 0);
    appearance: none;
    border-radius: var(--radius-md);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-input);
    background-color: transparent;
    padding-inline: calc(var(--spacing) * 3);
    padding-block: calc(var(--spacing) * 1);
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    transition-property: color,box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-outline-style: none;
    outline-style: none;
    & *::selection {
      background-color: var(--color-primary);
    }
    &::selection {
      background-color: var(--color-primary);
    }
    & *::selection {
      color: var(--color-primary-foreground);
    }
    &::selection {
      color: var(--color-primary-foreground);
    }
    &::file-selector-button {
      display: inline-flex;
    }
    &::file-selector-button {
      height: calc(var(--spacing) * 7);
    }
    &::file-selector-button {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
    &::file-selector-button {
      background-color: transparent;
    }
    &::file-selector-button {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
    &::file-selector-button {
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
    }
    &::file-selector-button {
      color: var(--color-foreground);
    }
    &::placeholder {
      color: var(--color-muted-foreground);
    }
    &:disabled {
      pointer-events: none;
    }
    &:disabled {
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 50%;
    }
    @media (width >= 48rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
    &:is(.dark *) {
      background-color: var(--color-input);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-input) 30%, transparent);
      }
    }
    &:focus-visible {
      border-color: var(--color-ring);
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--color-ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
      }
    }
    &[aria-invalid="true"] {
      border-color: var(--color-destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--color-destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
      }
    }
    &:is(.dark *) {
      &[aria-invalid="true"] {
        --tw-ring-color: var(--color-destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
        }
      }
    }
  }
}
@layer components {
  .form label, .label {
    display: flex;
    align-items: center;
    gap: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-leading: 1;
    line-height: 1;
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    -webkit-user-select: none;
    user-select: none;
    &:is(:where(.peer):disabled ~ *) {
      pointer-events: none;
    }
    &:is(:where(.peer):disabled ~ *) {
      opacity: 50%;
    }
    &:has(>*:disabled), &:has(+*:disabled) {
      pointer-events: none;
      opacity: 50%;
    }
  }
}
@layer components {
  [data-popover] {
    visibility: visible;
    position: absolute;
    z-index: 50;
    width: max-content;
    min-width: 100%;
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
    overflow-x: hidden;
    overflow-y: auto;
    border-radius: var(--radius-md);
    border-style: var(--tw-border-style);
    border-width: 1px;
    background-color: var(--color-popover);
    color: var(--color-popover-foreground);
    opacity: 100%;
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    &[aria-hidden='true'] {
      visibility: hidden;
      --tw-scale-x: 95%;
      --tw-scale-y: 95%;
      --tw-scale-z: 95%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
      opacity: 0%;
      &:not([data-side]), &[data-side='bottom'] {
        --tw-translate-y: calc(var(--spacing) * -2);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
      &[data-side='top'] {
        --tw-translate-y: calc(var(--spacing) * 2);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
      &[data-side='left'] {
        --tw-translate-x: calc(var(--spacing) * 2);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
      &[data-side='right'] {
        --tw-translate-x: calc(var(--spacing) * -2);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
    &:not([data-side]), &[data-side='bottom'] {
      top: 100%;
      margin-top: calc(var(--spacing) * 1);
    }
    &[data-side='top'] {
      bottom: 100%;
      margin-bottom: calc(var(--spacing) * 1);
    }
    &[data-side='left'] {
      right: 100%;
      margin-right: calc(var(--spacing) * 1);
    }
    &[data-side='right'] {
      left: 100%;
      margin-left: calc(var(--spacing) * 1);
    }
    &:not([data-side]), &[data-side='bottom'], &[data-side='top'] {
      &:not([data-align]), &[data-align='start'] {
        left: calc(var(--spacing) * 0);
      }
      &[data-align='end'] {
        right: calc(var(--spacing) * 0);
      }
      &[data-align='center'] {
        left: calc(1/2 * 100%);
        --tw-translate-x: calc(calc(1/2 * 100%) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
    &[data-side='left'], &[data-side='right'] {
      &:not([data-align]), &[data-align='start'] {
        top: calc(var(--spacing) * 0);
      }
      &[data-align='end'] {
        bottom: calc(var(--spacing) * 0);
      }
      &[data-align='center'] {
        top: calc(1/2 * 100%);
        --tw-translate-y: calc(calc(1/2 * 100%) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .popover {
    position: relative;
    display: inline-flex;
    [data-popover] {
      padding: calc(var(--spacing) * 4);
    }
  }
}
@layer components {
  .form input[type='radio'], .input[type='radio'] {
    position: relative;
    aspect-ratio: 1 / 1;
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
    flex-shrink: 0;
    appearance: none;
    border-radius: calc(infinity * 1px);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-input);
    color: var(--color-primary);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    transition-property: color,box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-outline-style: none;
    outline-style: none;
    &:focus-visible {
      border-color: var(--color-ring);
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--color-ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
      }
    }
    &:disabled {
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 50%;
    }
    &[aria-invalid="true"] {
      border-color: var(--color-destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--color-destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
      }
    }
    &:is(.dark *) {
      background-color: var(--color-input);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-input) 30%, transparent);
      }
    }
    &:is(.dark *) {
      &[aria-invalid="true"] {
        --tw-ring-color: var(--color-destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
        }
      }
    }
    &:checked:before {
      position: absolute;
      top: calc(1/2 * 100%);
      left: calc(1/2 * 100%);
      width: calc(var(--spacing) * 2);
      height: calc(var(--spacing) * 2);
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
      --tw-translate-y: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
      border-radius: calc(infinity * 1px);
      background-color: var(--color-primary);
      --tw-content: '';
      content: var(--tw-content);
    }
  }
}
@layer components {
  .form input[type='range'], .input[type='range'] {
    display: flex;
    appearance: none;
    align-items: center;
    padding: calc(var(--spacing) * 0);
    --tw-outline-style: none;
    outline-style: none;
    --slider-value: 20%;
    &:hover, &:focus-visible {
      &::-webkit-slider-thumb {
        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
      &::-moz-range-thumb {
        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
      &::-ms-thumb {
        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
    &::-webkit-slider-thumb {
      margin-top: calc(var(--spacing) * -1.25);
      display: block;
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      flex-shrink: 0;
      appearance: none;
      border-radius: calc(infinity * 1px);
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-primary);
      background-color: var(--color-background);
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      --tw-ring-color: var(--color-ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
      }
    }
    &::-webkit-slider-runnable-track {
      height: calc(var(--spacing) * 1.5);
      width: 100%;
      appearance: none;
      border-radius: calc(infinity * 1px);
      background: linear-gradient(to right, var(--primary) var(--slider-value), var(--muted) var(--slider-value));
    }
    &::-moz-range-thumb {
      margin-top: calc(var(--spacing) * -1.25);
      display: block;
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      flex-shrink: 0;
      appearance: none;
      border-radius: calc(infinity * 1px);
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-primary);
      background-color: var(--color-background);
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      --tw-ring-color: var(--color-ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
      }
    }
    &::-moz-range-track {
      height: calc(var(--spacing) * 1.5);
      width: 100%;
      appearance: none;
      border-radius: calc(infinity * 1px);
      background: linear-gradient(to right, var(--primary) var(--slider-value), var(--muted) var(--slider-value));
    }
    &::-ms-thumb {
      margin-top: calc(var(--spacing) * -1.25);
      display: block;
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      flex-shrink: 0;
      appearance: none;
      border-radius: calc(infinity * 1px);
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-primary);
      background-color: var(--color-background);
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      --tw-ring-color: var(--color-ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
      }
    }
    &::-ms-track {
      height: calc(var(--spacing) * 1.5);
      width: 100%;
      appearance: none;
      border-radius: calc(infinity * 1px);
    }
    &::-ms-fill-lower {
      border-radius: calc(infinity * 1px);
      background-color: var(--color-primary);
    }
    &::-ms-fill-upper {
      border-radius: calc(infinity * 1px);
      background-color: var(--color-muted);
    }
  }
}
@layer components {
  .form select, select.select {
    display: flex;
    height: calc(var(--spacing) * 9);
    width: fit-content;
    appearance: none;
    align-items: center;
    justify-content: space-between;
    gap: calc(var(--spacing) * 2);
    border-radius: var(--radius-md);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-input);
    background-color: transparent;
    padding-block: calc(var(--spacing) * 2);
    padding-right: calc(var(--spacing) * 9);
    padding-left: calc(var(--spacing) * 3);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    white-space: nowrap;
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    transition-property: color,box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-outline-style: none;
    outline-style: none;
    &:focus-visible {
      border-color: var(--color-ring);
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--color-ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
      }
    }
    &:disabled {
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 50%;
    }
    &[aria-invalid="true"] {
      border-color: var(--color-destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--color-destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
      }
    }
    &:is(.dark *) {
      background-color: var(--color-input);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-input) 30%, transparent);
      }
    }
    &:is(.dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-input);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-input) 50%, transparent);
          }
        }
      }
    }
    &:is(.dark *) {
      &[aria-invalid="true"] {
        --tw-ring-color: var(--color-destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
        }
      }
    }
    background-image: var(--chevron-down-icon-50);
    background-size: 1rem;
    background-position: center right 0.75rem;
    background-repeat: no-repeat;
    option, optgroup {
      background-color: var(--color-popover);
      color: var(--color-popover-foreground);
    }
  }
  *:not(select).select {
    position: relative;
    display: inline-flex;
    [data-popover] {
      padding: calc(var(--spacing) * 1);
      [role='option'] {
        position: relative;
        display: flex;
        width: 100%;
        cursor: default;
        align-items: center;
        gap: calc(var(--spacing) * 2);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        border-radius: var(--radius-sm);
        padding-block: calc(var(--spacing) * 1.5);
        padding-right: calc(var(--spacing) * 7.5);
        padding-left: calc(var(--spacing) * 2);
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
        -webkit-user-select: none;
        user-select: none;
        &:disabled {
          pointer-events: none;
        }
        &:disabled {
          opacity: 50%;
        }
        &[aria-disabled="true"] {
          pointer-events: none;
        }
        &[aria-disabled="true"] {
          opacity: 50%;
        }
        &[aria-hidden="true"] {
          display: none;
        }
        & svg {
          width: calc(var(--spacing) * 4);
          height: calc(var(--spacing) * 4);
        }
        & svg {
          flex-shrink: 0;
        }
        & svg {
          color: var(--color-muted-foreground);
        }
        &[aria-selected='true'] {
          background-image: var(--check-icon);
          background-size: 0.875rem;
          background-position: center right 0.5rem;
          background-repeat: no-repeat;
        }
        &.active, &:focus-visible {
          background-color: var(--color-accent);
          color: var(--color-accent-foreground);
        }
      }
      [role='listbox'] [role='heading'] {
        display: flex;
        padding-inline: calc(var(--spacing) * 2);
        padding-block: calc(var(--spacing) * 1.5);
        font-size: var(--text-xs);
        line-height: var(--tw-leading, var(--text-xs--line-height));
        color: var(--color-muted-foreground);
      }
      [role='listbox'] [role='group']:not(:has([role='option']:not([aria-hidden='true']))) {
        display: none;
      }
      [role='separator'] {
        margin-inline: calc(var(--spacing) * -1);
        margin-block: calc(var(--spacing) * 1);
        border-color: var(--color-border);
      }
      > header {
        margin-inline: calc(var(--spacing) * -1);
        margin-top: calc(var(--spacing) * -1);
        margin-bottom: calc(var(--spacing) * 1);
        display: flex;
        height: calc(var(--spacing) * 9);
        align-items: center;
        gap: calc(var(--spacing) * 2);
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 1px;
        padding-inline: calc(var(--spacing) * 3);
        svg {
          width: calc(var(--spacing) * 4);
          height: calc(var(--spacing) * 4);
          flex-shrink: 0;
          opacity: 50%;
        }
        input[role='combobox'] {
          display: flex;
          height: calc(var(--spacing) * 10);
          width: 100%;
          min-width: calc(var(--spacing) * 0);
          flex: 1;
          border-radius: var(--radius-md);
          background-color: transparent;
          padding-block: calc(var(--spacing) * 3);
          font-size: var(--text-sm);
          line-height: var(--tw-leading, var(--text-sm--line-height));
          --tw-outline-style: none;
          outline-style: none;
          @media (forced-colors: active) {
            outline: 2px solid transparent;
            outline-offset: 2px;
          }
          &::placeholder {
            color: var(--color-muted-foreground);
          }
          &:disabled {
            cursor: not-allowed;
          }
          &:disabled {
            opacity: 50%;
          }
        }
      }
      [role='listbox']:not(:has([data-value]:not([aria-hidden='true'])))::before {
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: calc(var(--spacing) * 6);
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
      }
      [role='listbox'][data-empty]:not(:has([data-value]:not([aria-hidden='true'])))::before {
        --tw-content: attr(data-empty);
        content: var(--tw-content);
      }
      [role='listbox']:not([data-empty]):not(:has([data-value]:not([aria-hidden='true'])))::before {
        --tw-content: 'No results found';
        content: var(--tw-content);
      }
    }
    &:not([data-select-initialized]) [data-popover] [role='option'] {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-accent);
        }
      }
      &:hover {
        @media (hover: hover) {
          color: var(--color-accent-foreground);
        }
      }
    }
  }
}
.sidebar {
  &:not([data-sidebar-initialized]) {
    @media (width < 48rem) {
      display: none;
    }
  }
  &:not([aria-hidden]), &[aria-hidden=false] {
    @media (width < 48rem) {
      position: fixed;
    }
    @media (width < 48rem) {
      inset: calc(var(--spacing) * 0);
    }
    @media (width < 48rem) {
      z-index: 40;
    }
    @media (width < 48rem) {
      background-color: color-mix(in srgb, #000 50%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
      }
    }
  }
  nav {
    position: fixed;
    inset-block: calc(var(--spacing) * 0);
    z-index: 50;
    display: flex;
    width: var(--sidebar-mobile-width);
    flex-direction: column;
    background-color: var(--color-sidebar);
    color: var(--color-sidebar-foreground);
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 300ms;
    transition-duration: 300ms;
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
    @media (width >= 48rem) {
      width: var(--sidebar-width);
    }
  }
  & + * {
    transition-property: margin;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 300ms;
    transition-duration: 300ms;
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  &:not([data-side]), &[data-side=left] {
    nav {
      left: calc(var(--spacing) * 0);
      border-right-style: var(--tw-border-style);
      border-right-width: 1px;
    }
    & + * {
      position: relative;
      @media (width >= 48rem) {
        margin-left: var(--sidebar-width);
      }
    }
    &[aria-hidden=true] {
      nav {
        --tw-translate-x: -100%;
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
      & + * {
        @media (width >= 48rem) {
          margin-left: calc(var(--spacing) * 0);
        }
      }
    }
  }
  &[data-side=right] {
    nav {
      right: calc(var(--spacing) * 0);
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
    & + * {
      position: relative;
      @media (width >= 48rem) {
        margin-right: var(--sidebar-width);
      }
    }
    &[aria-hidden=true] {
      nav {
        --tw-translate-x: 100%;
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
      & + * {
        @media (width >= 48rem) {
          margin-right: calc(var(--spacing) * 0);
        }
      }
    }
  }
  nav {
    > header, > footer {
      display: flex;
      flex-direction: column;
      gap: calc(var(--spacing) * 2);
      padding: calc(var(--spacing) * 2);
    }
    [role=separator] {
      margin-inline: calc(var(--spacing) * 2);
      width: auto;
      border-color: var(--color-sidebar-border);
    }
    > section {
      display: flex;
      min-height: calc(var(--spacing) * 0);
      flex: 1;
      flex-direction: column;
      gap: calc(var(--spacing) * 2);
      overflow-y: auto;
      > [role=group] {
        position: relative;
        display: flex;
        width: 100%;
        min-width: calc(var(--spacing) * 0);
        flex-direction: column;
        padding: calc(var(--spacing) * 2);
      }
      h3 {
        display: flex;
        height: calc(var(--spacing) * 8);
        flex-shrink: 0;
        align-items: center;
        border-radius: var(--radius-md);
        padding-inline: calc(var(--spacing) * 2);
        font-size: var(--text-xs);
        line-height: var(--tw-leading, var(--text-xs--line-height));
        --tw-font-weight: var(--font-weight-medium);
        font-weight: var(--font-weight-medium);
        color: var(--color-sidebar-foreground);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-sidebar-foreground) 70%, transparent);
        }
        --tw-ring-color: var(--color-sidebar-ring);
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
        transition-property: margin,opacity;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
        --tw-duration: 200ms;
        transition-duration: 200ms;
        --tw-ease: linear;
        transition-timing-function: linear;
        &:focus-visible {
          --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
          box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
        &>svg {
          width: calc(var(--spacing) * 4);
          height: calc(var(--spacing) * 4);
        }
        &>svg {
          flex-shrink: 0;
        }
      }
      ul {
        display: flex;
        width: 100%;
        min-width: calc(var(--spacing) * 0);
        flex-direction: column;
        gap: calc(var(--spacing) * 1);
        li {
          position: relative;
          > a, > details > summary {
            display: flex;
            width: 100%;
            align-items: center;
            gap: calc(var(--spacing) * 2);
            overflow: hidden;
            border-radius: var(--radius-md);
            padding: calc(var(--spacing) * 2);
            text-align: left;
            font-size: var(--text-sm);
            line-height: var(--tw-leading, var(--text-sm--line-height));
            --tw-ring-color: var(--color-sidebar-ring);
            --tw-outline-style: none;
            outline-style: none;
            @media (forced-colors: active) {
              outline: 2px solid transparent;
              outline-offset: 2px;
            }
            transition-property: width,height,padding;
            transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
            transition-duration: var(--tw-duration, var(--default-transition-duration));
            &:hover {
              @media (hover: hover) {
                background-color: var(--color-sidebar-accent);
              }
            }
            &:hover {
              @media (hover: hover) {
                color: var(--color-sidebar-accent-foreground);
              }
            }
            &:focus-visible {
              --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
              box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
            }
            &:active {
              background-color: var(--color-sidebar-accent);
            }
            &:active {
              color: var(--color-sidebar-accent-foreground);
            }
            &:disabled {
              pointer-events: none;
            }
            &:disabled {
              opacity: 50%;
            }
            &[aria-disabled="true"] {
              pointer-events: none;
            }
            &[aria-disabled="true"] {
              opacity: 50%;
            }
            &>span:last-child {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            &>svg {
              width: calc(var(--spacing) * 4);
              height: calc(var(--spacing) * 4);
            }
            &>svg {
              flex-shrink: 0;
            }
            &[aria-current=page] {
              background-color: var(--color-sidebar-accent);
            }
            &[aria-current=page] {
              --tw-font-weight: var(--font-weight-medium);
              font-weight: var(--font-weight-medium);
            }
            &[aria-current=page] {
              color: var(--color-sidebar-accent-foreground);
            }
            &:not([data-variant]), &[data-variant=default] {
              &:hover {
                @media (hover: hover) {
                  background-color: var(--color-sidebar-accent);
                }
              }
              &:hover {
                @media (hover: hover) {
                  color: var(--color-sidebar-accent-foreground);
                }
              }
            }
            &[data-variant=outline] {
              background-color: var(--color-background);
              --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));
              box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
              &:hover {
                @media (hover: hover) {
                  background-color: var(--color-sidebar-accent);
                }
              }
              &:hover {
                @media (hover: hover) {
                  color: var(--color-sidebar-accent-foreground);
                }
              }
              &:hover {
                @media (hover: hover) {
                  --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));
                  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
                }
              }
            }
            &:not([data-size]), &[data-size=default] {
              height: calc(var(--spacing) * 8);
              font-size: var(--text-sm);
              line-height: var(--tw-leading, var(--text-sm--line-height));
            }
            &[data-size=sm] {
              height: calc(var(--spacing) * 7);
              font-size: var(--text-xs);
              line-height: var(--tw-leading, var(--text-xs--line-height));
            }
            &[data-size=lg] {
              height: calc(var(--spacing) * 12);
              font-size: var(--text-sm);
              line-height: var(--tw-leading, var(--text-sm--line-height));
              &:is(:where(.group)[data-collapsible="icon"] *) {
                padding: calc(var(--spacing) * 0) !important;
              }
            }
          }
          > details {
            &:not([open]) {
              > summary {
                &::after {
                  rotate: calc(90deg * -1);
                }
              }
            }
            > summary {
              &::after {
                margin-left: auto;
                display: block;
                width: calc(var(--spacing) * 3.5);
                height: calc(var(--spacing) * 3.5);
                background-color: var(--color-primary);
                transition-property: transform, translate, scale, rotate;
                transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
                transition-duration: var(--tw-duration, var(--default-transition-duration));
                --tw-ease: linear;
                transition-timing-function: linear;
                --tw-content: '';
                content: var(--tw-content);
                mask-image: var(--chevron-down-icon);
                mask-size: 1rem;
                mask-position: center;
                mask-repeat: no-repeat;
              }
            }
            &::details-content {
              padding-inline: calc(var(--spacing) * 3.5);
            }
          }
        }
        ul {
          display: flex;
          width: 100%;
          min-width: calc(var(--spacing) * 0);
          --tw-translate-x: 1px;
          translate: var(--tw-translate-x) var(--tw-translate-y);
          flex-direction: column;
          gap: calc(var(--spacing) * 1);
          border-left-style: var(--tw-border-style);
          border-left-width: 1px;
          border-color: var(--color-sidebar-border);
          padding-inline: calc(var(--spacing) * 2.5);
          padding-block: calc(var(--spacing) * 0.5);
        }
      }
    }
  }
}
@layer components {
  .form input[type='checkbox'][role='switch'], .input[type='checkbox'][role='switch'] {
    display: inline-flex;
    height: 1.15rem;
    width: calc(var(--spacing) * 8);
    flex-shrink: 0;
    appearance: none;
    align-items: center;
    border-radius: calc(infinity * 1px);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: transparent;
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-outline-style: none;
    outline-style: none;
    &:focus-visible {
      border-color: var(--color-ring);
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--color-ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
      }
    }
    &:disabled {
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 50%;
    }
    background-color: var(--color-input);
    &:checked {
      background-color: var(--color-primary);
    }
    &:is(.dark *) {
      background-color: var(--color-input);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-input) 80%, transparent);
      }
    }
    &:is(.dark *) {
      &:checked {
        background-color: var(--color-primary);
      }
    }
    &::before {
      content: var(--tw-content);
      pointer-events: none;
    }
    &::before {
      content: var(--tw-content);
      display: block;
    }
    &::before {
      content: var(--tw-content);
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
    &::before {
      content: var(--tw-content);
      border-radius: calc(infinity * 1px);
    }
    &::before {
      content: var(--tw-content);
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &::before {
      content: var(--tw-content);
      transition-property: all;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
    }
    &::before {
      content: var(--tw-content);
      --tw-content: '';
      content: var(--tw-content);
    }
    &::before {
      content: var(--tw-content);
      background-color: var(--color-background);
    }
    &:is(.dark *) {
      &::before {
        content: var(--tw-content);
        background-color: var(--color-foreground);
      }
    }
    &:checked {
      &::before {
        content: var(--tw-content);
        margin-inline-start: calc(var(--spacing) * 3.5);
      }
    }
    &:is(.dark *) {
      &:checked {
        &::before {
          content: var(--tw-content);
          background-color: var(--color-primary-foreground);
        }
      }
    }
  }
}
@layer components {
  .table {
    width: 100%;
    caption-side: bottom;
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    thead {
      & tr {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 1px;
      }
    }
    tbody {
      & tr:last-child {
        border-style: var(--tw-border-style);
        border-width: 0px;
      }
    }
    tfoot {
      border-top-style: var(--tw-border-style);
      border-top-width: 1px;
      background-color: var(--color-muted);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-muted) 50%, transparent);
      }
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
      &>tr {
        &:last-child {
          border-bottom-style: var(--tw-border-style);
          border-bottom-width: 0px;
        }
      }
    }
    tr {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 1px;
      transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-muted);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-muted) 50%, transparent);
          }
        }
      }
    }
    th {
      height: calc(var(--spacing) * 10);
      padding-inline: calc(var(--spacing) * 2);
      text-align: left;
      vertical-align: middle;
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
      white-space: nowrap;
      color: var(--color-foreground);
      &:has([role=checkbox]) {
        padding-right: calc(var(--spacing) * 0);
      }
      &>[role=checkbox] {
        --tw-translate-y: 2px;
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
    td {
      padding: calc(var(--spacing) * 2);
      vertical-align: middle;
      white-space: nowrap;
      &:has([role=checkbox]) {
        padding-right: calc(var(--spacing) * 0);
      }
      &>[role=checkbox] {
        --tw-translate-y: 2px;
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
    caption {
      margin-top: calc(var(--spacing) * 4);
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
      color: var(--color-muted-foreground);
    }
  }
}
@layer components {
  .tabs {
    display: flex;
    flex-direction: column;
    gap: calc(var(--spacing) * 2);
    [role='tablist'] {
      display: inline-flex;
      height: calc(var(--spacing) * 9);
      width: fit-content;
      align-items: center;
      justify-content: center;
      border-radius: var(--radius-lg);
      background-color: var(--color-muted);
      padding: 3px;
      color: var(--color-muted-foreground);
      [role='tab'] {
        display: inline-flex;
        height: calc(100% - 1px);
        flex: 1;
        align-items: center;
        justify-content: center;
        gap: calc(var(--spacing) * 1.5);
        border-radius: var(--radius-md);
        border-style: var(--tw-border-style);
        border-width: 1px;
        border-color: transparent;
        padding-inline: calc(var(--spacing) * 2);
        padding-block: calc(var(--spacing) * 1);
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
        --tw-font-weight: var(--font-weight-medium);
        font-weight: var(--font-weight-medium);
        white-space: nowrap;
        color: var(--color-foreground);
        transition-property: color,box-shadow;
        transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
        transition-duration: var(--tw-duration, var(--default-transition-duration));
        &:focus-visible {
          border-color: var(--color-ring);
        }
        &:focus-visible {
          --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
          box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
        &:focus-visible {
          --tw-ring-color: var(--color-ring);
          @supports (color: color-mix(in lab, red, red)) {
            --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
          }
        }
        &:focus-visible {
          outline-style: var(--tw-outline-style);
          outline-width: 1px;
        }
        &:focus-visible {
          outline-color: var(--color-ring);
        }
        &:disabled {
          pointer-events: none;
        }
        &:disabled {
          opacity: 50%;
        }
        &:is(.dark *) {
          color: var(--color-muted-foreground);
        }
        & svg {
          pointer-events: none;
        }
        & svg {
          flex-shrink: 0;
        }
        & svg:not([class*='size-']) {
          width: calc(var(--spacing) * 4);
          height: calc(var(--spacing) * 4);
        }
        &[aria-selected='true'] {
          background-color: var(--color-background);
          --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
          box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
          &:is(.dark *) {
            border-color: var(--color-input);
          }
          &:is(.dark *) {
            background-color: var(--color-input);
            @supports (color: color-mix(in lab, red, red)) {
              background-color: color-mix(in oklab, var(--color-input) 30%, transparent);
            }
          }
          &:is(.dark *) {
            color: var(--color-foreground);
          }
        }
      }
    }
    [role='tabpanel'] {
      flex: 1;
      --tw-outline-style: none;
      outline-style: none;
    }
  }
}
@layer components {
  .form textarea, .textarea {
    display: flex;
    field-sizing: content;
    min-height: calc(var(--spacing) * 16);
    width: 100%;
    border-radius: var(--radius-md);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--color-input);
    background-color: transparent;
    padding-inline: calc(var(--spacing) * 3);
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    transition-property: color,box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-outline-style: none;
    outline-style: none;
    &::placeholder {
      color: var(--color-muted-foreground);
    }
    &:focus-visible {
      border-color: var(--color-ring);
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--color-ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
      }
    }
    &:disabled {
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 50%;
    }
    &[aria-invalid="true"] {
      border-color: var(--color-destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--color-destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--color-destructive) 20%, transparent);
      }
    }
    @media (width >= 48rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
    &:is(.dark *) {
      background-color: var(--color-input);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-input) 30%, transparent);
      }
    }
    &:is(.dark *) {
      &[aria-invalid="true"] {
        --tw-ring-color: var(--color-destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--color-destructive) 40%, transparent);
        }
      }
    }
  }
}
@layer components {
  .toaster {
    pointer-events: none;
    position: fixed;
    bottom: calc(var(--spacing) * 0);
    z-index: 50;
    display: flex;
    width: 100%;
    flex-direction: column-reverse;
    padding: calc(var(--spacing) * 4);
    @media (width >= 40rem) {
      max-width: calc(var(--spacing) * 90);
    }
    &:not([data-align]), &[data-align='end'] {
      right: calc(var(--spacing) * 0);
    }
    &[data-align='start'] {
      left: calc(var(--spacing) * 0);
    }
    &[data-align='center'] {
      left: calc(1/2 * 100%);
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    .toast {
      pointer-events: auto;
      margin-top: calc(var(--spacing) * 4);
      display: grid;
      width: 100%;
      animation: toast-up 0.3s ease-in-out;
      grid-template-rows: 1fr;
      transition-property: grid-template-rows,opacity,margin;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      --tw-duration: 300ms;
      transition-duration: 300ms;
      --tw-ease: var(--ease-in-out);
      transition-timing-function: var(--ease-in-out);
      .toast-content {
        display: flex;
        align-items: center;
        gap: calc(var(--spacing) * 2.5);
        overflow: hidden;
        border-radius: var(--radius-lg);
        border-style: var(--tw-border-style);
        border-width: 1px;
        background-color: var(--color-popover);
        padding: calc(var(--spacing) * 3);
        font-size: 13px;
        color: var(--color-popover-foreground);
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        svg {
          width: calc(var(--spacing) * 4);
          height: calc(var(--spacing) * 4);
          flex-shrink: 0;
        }
        section {
          h2 {
            --tw-font-weight: var(--font-weight-medium);
            font-weight: var(--font-weight-medium);
            --tw-tracking: var(--tracking-tight);
            letter-spacing: var(--tracking-tight);
          }
          p {
            color: var(--color-muted-foreground);
          }
        }
        footer {
          margin-left: auto;
          display: flex;
          flex-direction: column;
          gap: calc(var(--spacing) * 2);
          [data-toast-action], [data-toast-cancel] {
            height: calc(var(--spacing) * 6);
            padding-inline: calc(var(--spacing) * 2.5);
            font-size: var(--text-xs);
            line-height: var(--tw-leading, var(--text-xs--line-height));
          }
        }
      }
      &[aria-hidden='true'] {
        margin: calc(var(--spacing) * 0);
        grid-template-rows: 0fr;
        overflow: hidden;
        border-style: var(--tw-border-style);
        border-width: 0px;
        padding: calc(var(--spacing) * 0);
        opacity: 0%;
        .toast-content {
          border-style: var(--tw-border-style);
          border-width: 0px;
        }
      }
    }
  }
}
@keyframes toast-up {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
}
@layer components {
  [data-tooltip] {
    position: relative;
    &:before {
      pointer-events: none;
      visibility: hidden;
      position: absolute;
      z-index: 50;
      width: fit-content;
      max-width: var(--container-xs);
      --tw-scale-x: 95%;
      --tw-scale-y: 95%;
      --tw-scale-z: 95%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      border-radius: var(--radius-md);
      background-color: var(--color-primary);
      padding-inline: calc(var(--spacing) * 3);
      padding-block: calc(var(--spacing) * 1.5);
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
      color: var(--color-primary-foreground);
      opacity: 0%;
      transition-property: all;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      --tw-content: attr(data-tooltip);
      content: var(--tw-content);
    }
    &:hover:before {
      visibility: visible;
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
      opacity: 100%;
    }
    &:focus-visible:not(:hover):before {
      display: none;
    }
    &:not([data-side]), &[data-side='top'] {
      &::before {
        content: var(--tw-content);
        bottom: 100%;
      }
      &::before {
        content: var(--tw-content);
        margin-bottom: calc(var(--spacing) * 1.5);
      }
      &::before {
        content: var(--tw-content);
        --tw-translate-y: calc(var(--spacing) * 2);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
      &:hover {
        @media (hover: hover) {
          &::before {
            content: var(--tw-content);
            --tw-translate-y: calc(var(--spacing) * 0);
            translate: var(--tw-translate-x) var(--tw-translate-y);
          }
        }
      }
    }
    &[data-side='bottom'] {
      &::before {
        content: var(--tw-content);
        top: 100%;
      }
      &::before {
        content: var(--tw-content);
        margin-top: calc(var(--spacing) * 1.5);
      }
      &::before {
        content: var(--tw-content);
        --tw-translate-y: calc(var(--spacing) * -2);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
      &:hover {
        @media (hover: hover) {
          &::before {
            content: var(--tw-content);
            --tw-translate-y: calc(var(--spacing) * 0);
            translate: var(--tw-translate-x) var(--tw-translate-y);
          }
        }
      }
    }
    &:not([data-side]), &[data-side='top'], &[data-side='bottom'] {
      &[data-align='start'] {
        &::before {
          content: var(--tw-content);
          left: calc(var(--spacing) * 0);
        }
      }
      &[data-align='end'] {
        &::before {
          content: var(--tw-content);
          right: calc(var(--spacing) * 0);
        }
      }
      &:not([data-align]), &[data-align='center'] {
        &::before {
          content: var(--tw-content);
          left: calc(1/2 * 100%);
        }
        &::before {
          content: var(--tw-content);
          --tw-translate-x: calc(calc(1/2 * 100%) * -1);
          translate: var(--tw-translate-x) var(--tw-translate-y);
        }
      }
    }
    &[data-side='left'] {
      &::before {
        content: var(--tw-content);
        right: 100%;
      }
      &::before {
        content: var(--tw-content);
        margin-right: calc(var(--spacing) * 1.5);
      }
      &::before {
        content: var(--tw-content);
        --tw-translate-x: calc(var(--spacing) * 2);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
      &:hover {
        @media (hover: hover) {
          &::before {
            content: var(--tw-content);
            --tw-translate-x: calc(var(--spacing) * 0);
            translate: var(--tw-translate-x) var(--tw-translate-y);
          }
        }
      }
    }
    &[data-side='right'] {
      &::before {
        content: var(--tw-content);
        left: 100%;
      }
      &::before {
        content: var(--tw-content);
        margin-left: calc(var(--spacing) * 1.5);
      }
      &::before {
        content: var(--tw-content);
        --tw-translate-x: calc(var(--spacing) * -2);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
      &:hover {
        @media (hover: hover) {
          &::before {
            content: var(--tw-content);
            --tw-translate-x: calc(var(--spacing) * 0);
            translate: var(--tw-translate-x) var(--tw-translate-y);
          }
        }
      }
    }
    &[data-side='left'], &[data-side='right'] {
      &[data-align='start'] {
        &::before {
          content: var(--tw-content);
          top: calc(var(--spacing) * 0);
        }
      }
      &[data-align='end'] {
        &::before {
          content: var(--tw-content);
          bottom: calc(var(--spacing) * 0);
        }
      }
      &:not([data-align]), &[data-align='center'] {
        &::before {
          content: var(--tw-content);
          top: calc(1/2 * 100%);
        }
        &::before {
          content: var(--tw-content);
          --tw-translate-y: calc(calc(1/2 * 100%) * -1);
          translate: var(--tw-translate-x) var(--tw-translate-y);
        }
      }
    }
  }
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-border-style: solid;
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-leading: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-content: "";
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-outline-style: solid;
    }
  }
}
