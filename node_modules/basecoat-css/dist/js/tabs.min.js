(()=>{const e=e=>{const t=e.querySelector('[role="tablist"]');if(!t)return;const a=Array.from(t.querySelectorAll('[role="tab"]')),r=a.map((e=>document.getElementById(e.getAttribute("aria-controls")))).filter(Boolean),n=e=>{a.forEach(((e,t)=>{e.setAttribute("aria-selected","false"),e.setAttribute("tabindex","-1"),r[t]&&(r[t].hidden=!0)})),e.setAttribute("aria-selected","true"),e.setAttribute("tabindex","0");const t=document.getElementById(e.getAttribute("aria-controls"));t&&(t.hidden=!1)};t.addEventListener("click",(e=>{const t=e.target.closest('[role="tab"]');t&&n(t)})),t.addEventListener("keydown",(e=>{const t=e.target;if(!a.includes(t))return;let r;const i=a.indexOf(t);switch(e.key){case"ArrowRight":r=a[(i+1)%a.length];break;case"ArrowLeft":r=a[(i-1+a.length)%a.length];break;case"Home":r=a[0];break;case"End":r=a[a.length-1];break;default:return}e.preventDefault(),n(r),r.focus()})),e.dataset.tabsInitialized=!0,e.dispatchEvent(new CustomEvent("basecoat:initialized"))};document.querySelectorAll(".tabs:not([data-tabs-initialized])").forEach(e);new MutationObserver((t=>{t.forEach((t=>{t.addedNodes.forEach((t=>{t.nodeType===Node.ELEMENT_NODE&&(t.matches(".tabs:not([data-tabs-initialized])")&&e(t),t.querySelectorAll(".tabs:not([data-tabs-initialized])").forEach(e))}))}))})).observe(document.body,{childList:!0,subtree:!0})})();