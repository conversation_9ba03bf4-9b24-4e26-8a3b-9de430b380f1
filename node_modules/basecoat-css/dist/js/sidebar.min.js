(()=>{if(!window.history.__basecoatPatched){const e=window.history.pushState;window.history.pushState=function(...t){e.apply(this,t),window.dispatchEvent(new Event("basecoat:locationchange"))};const t=window.history.replaceState;window.history.replaceState=function(...e){t.apply(this,e),window.dispatchEvent(new Event("basecoat:locationchange"))},window.history.__basecoatPatched=!0}const e=e=>{const t="false"!==e.dataset.initialOpen,a="true"===e.dataset.initialMobileOpen,i=parseInt(e.dataset.breakpoint)||768;let n=i>0?window.innerWidth>=i?t:a:t;const o=()=>{const t=window.location.pathname.replace(/\/$/,"");e.querySelectorAll("a").forEach((e=>{if(e.hasAttribute("data-ignore-current"))return;new URL(e.href).pathname.replace(/\/$/,"")===t?e.setAttribute("aria-current","page"):e.removeAttribute("aria-current")}))},d=()=>{e.setAttribute("aria-hidden",!n),n?e.removeAttribute("inert"):e.setAttribute("inert","")},r=e=>{n=e,d()},c=e.id;document.addEventListener("basecoat:sidebar",(e=>{if(!e.detail?.id||e.detail.id===c)switch(e.detail?.action){case"open":r(!0);break;case"close":r(!1);break;default:r(!n)}})),e.addEventListener("click",(t=>{const a=t.target,n=e.querySelector("nav");if(window.innerWidth<i&&a.closest("a, button")&&!a.closest("[data-keep-mobile-sidebar-open]"))return document.activeElement&&document.activeElement.blur(),void r(!1);(a===e||n&&!n.contains(a))&&(document.activeElement&&document.activeElement.blur(),r(!1))})),window.addEventListener("popstate",o),window.addEventListener("basecoat:locationchange",o),d(),o(),e.dataset.sidebarInitialized=!0,e.dispatchEvent(new CustomEvent("basecoat:initialized"))};document.querySelectorAll(".sidebar:not([data-sidebar-initialized])").forEach(e);new MutationObserver((t=>{t.forEach((t=>{t.addedNodes.forEach((t=>{t.nodeType===Node.ELEMENT_NODE&&(t.matches(".sidebar:not([data-sidebar-initialized])")&&e(t),t.querySelectorAll(".sidebar:not([data-sidebar-initialized])").forEach(e))}))}))})).observe(document.body,{childList:!0,subtree:!0})})();