/*! tailwindcss v4.1.4 | MIT License | https://tailwindcss.com */
@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-border-style:solid;--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-font-weight:initial;--tw-tracking:initial;--tw-leading:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-content:"";--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-duration:initial;--tw-ease:initial;--tw-outline-style:solid}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-black:#000;--color-white:#fff;--spacing:.25rem;--container-xs:20rem;--container-lg:32rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height:calc(1.5/1);--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--font-weight-medium:500;--font-weight-semibold:600;--tracking-tight:-.025em;--leading-relaxed:1.625;--radius-xs:.125rem;--radius-sm:calc(var(--radius) - 4px);--radius-md:calc(var(--radius) - 2px);--radius-lg:var(--radius);--radius-xl:calc(var(--radius) + 4px);--ease-in-out:cubic-bezier(.4,0,.2,1);--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--color-background:var(--background);--color-foreground:var(--foreground);--color-card:var(--card);--color-card-foreground:var(--card-foreground);--color-popover:var(--popover);--color-popover-foreground:var(--popover-foreground);--color-primary:var(--primary);--color-primary-foreground:var(--primary-foreground);--color-secondary:var(--secondary);--color-secondary-foreground:var(--secondary-foreground);--color-muted:var(--muted);--color-muted-foreground:var(--muted-foreground);--color-accent:var(--accent);--color-accent-foreground:var(--accent-foreground);--color-destructive:var(--destructive);--color-border:var(--border);--color-input:var(--input);--color-ring:var(--ring);--color-sidebar:var(--sidebar);--color-sidebar-foreground:var(--sidebar-foreground);--color-sidebar-accent:var(--sidebar-accent);--color-sidebar-accent-foreground:var(--sidebar-accent-foreground);--color-sidebar-border:var(--sidebar-border);--color-sidebar-ring:var(--sidebar-ring)}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab, red, red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){appearance:button}::file-selector-button{appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}*{border-color:var(--color-border);outline-color:var(--color-ring)}@supports (color:color-mix(in lab, red, red)){*{outline-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}html{scroll-behavior:smooth}body{overscroll-behavior:none;background-color:var(--color-background);color:var(--color-foreground);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.scrollbar{scrollbar-width:thin;scrollbar-color:var(--scrollbar-thumb)var(--scrollbar-track)}.scrollbar::-webkit-scrollbar{width:var(--scrollbar-width)}.scrollbar::-webkit-scrollbar-track{background:var(--scrollbar-track)}.scrollbar::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:var(--scrollbar-radius)}[x-cloak]{display:none!important}}@layer components{.alert,.alert-destructive{align-items:flex-start;row-gap:calc(var(--spacing)*.5);border-radius:var(--radius-lg);border-style:var(--tw-border-style);width:100%;padding-inline:calc(var(--spacing)*4);padding-block:calc(var(--spacing)*3);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));border-width:1px;grid-template-columns:0 1fr;display:grid;position:relative}:is(.alert,.alert-destructive):has(>svg){grid-template-columns:calc(var(--spacing)*4)1fr;column-gap:calc(var(--spacing)*3)}:is(.alert,.alert-destructive)>svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);--tw-translate-y:calc(var(--spacing)*.5);translate:var(--tw-translate-x)var(--tw-translate-y);color:currentColor}:is(.alert,.alert-destructive) h2{-webkit-line-clamp:1;min-height:calc(var(--spacing)*4);--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);--tw-tracking:var(--tracking-tight);letter-spacing:var(--tracking-tight);-webkit-box-orient:vertical;grid-column-start:2;display:-webkit-box;overflow:hidden}:is(.alert,.alert-destructive) section{justify-items:start;gap:calc(var(--spacing)*1);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));color:var(--color-muted-foreground);grid-column-start:2;display:grid}:is(.alert,.alert-destructive) section p{--tw-leading:var(--leading-relaxed);line-height:var(--leading-relaxed)}:is(.alert,.alert-destructive) section ul{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));list-style-type:disc;list-style-position:inside}.alert{background-color:var(--color-card);color:var(--color-card-foreground)}.alert-destructive{background-color:var(--color-card);color:var(--color-destructive)}.alert-destructive>svg{color:currentColor}.alert-destructive section{color:var(--color-destructive)}.btn,.btn-primary,.btn-secondary,.btn-outline,.btn-ghost,.btn-link,.btn-destructive,.btn-sm,.btn-sm-primary,.btn-sm-secondary,.btn-sm-outline,.btn-sm-ghost,.btn-sm-link,.btn-sm-destructive,.btn-lg,.btn-lg-primary,.btn-lg-secondary,.btn-lg-outline,.btn-lg-ghost,.btn-lg-link,.btn-lg-destructive,.btn-icon,.btn-icon-primary,.btn-icon-secondary,.btn-icon-outline,.btn-icon-ghost,.btn-icon-link,.btn-icon-destructive,.btn-sm-icon,.btn-sm-icon-primary,.btn-sm-icon-secondary,.btn-sm-icon-outline,.btn-sm-icon-ghost,.btn-sm-icon-link,.btn-sm-icon-destructive,.btn-lg-icon,.btn-lg-icon-primary,.btn-lg-icon-secondary,.btn-lg-icon-outline,.btn-lg-icon-ghost,.btn-lg-icon-link,.btn-lg-icon-destructive{cursor:pointer;border-radius:var(--radius-md);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);white-space:nowrap;transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-outline-style:none;outline-style:none;flex-shrink:0;justify-content:center;align-items:center;display:inline-flex}:is(.btn,.btn-primary,.btn-secondary,.btn-outline,.btn-ghost,.btn-link,.btn-destructive,.btn-sm,.btn-sm-primary,.btn-sm-secondary,.btn-sm-outline,.btn-sm-ghost,.btn-sm-link,.btn-sm-destructive,.btn-lg,.btn-lg-primary,.btn-lg-secondary,.btn-lg-outline,.btn-lg-ghost,.btn-lg-link,.btn-lg-destructive,.btn-icon,.btn-icon-primary,.btn-icon-secondary,.btn-icon-outline,.btn-icon-ghost,.btn-icon-link,.btn-icon-destructive,.btn-sm-icon,.btn-sm-icon-primary,.btn-sm-icon-secondary,.btn-sm-icon-outline,.btn-sm-icon-ghost,.btn-sm-icon-link,.btn-sm-icon-destructive,.btn-lg-icon,.btn-lg-icon-primary,.btn-lg-icon-secondary,.btn-lg-icon-outline,.btn-lg-icon-ghost,.btn-lg-icon-link,.btn-lg-icon-destructive):focus-visible{border-color:var(--color-ring);--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring)}@supports (color:color-mix(in lab, red, red)){:is(.btn,.btn-primary,.btn-secondary,.btn-outline,.btn-ghost,.btn-link,.btn-destructive,.btn-sm,.btn-sm-primary,.btn-sm-secondary,.btn-sm-outline,.btn-sm-ghost,.btn-sm-link,.btn-sm-destructive,.btn-lg,.btn-lg-primary,.btn-lg-secondary,.btn-lg-outline,.btn-lg-ghost,.btn-lg-link,.btn-lg-destructive,.btn-icon,.btn-icon-primary,.btn-icon-secondary,.btn-icon-outline,.btn-icon-ghost,.btn-icon-link,.btn-icon-destructive,.btn-sm-icon,.btn-sm-icon-primary,.btn-sm-icon-secondary,.btn-sm-icon-outline,.btn-sm-icon-ghost,.btn-sm-icon-link,.btn-sm-icon-destructive,.btn-lg-icon,.btn-lg-icon-primary,.btn-lg-icon-secondary,.btn-lg-icon-outline,.btn-lg-icon-ghost,.btn-lg-icon-link,.btn-lg-icon-destructive):focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}:is(.btn,.btn-primary,.btn-secondary,.btn-outline,.btn-ghost,.btn-link,.btn-destructive,.btn-sm,.btn-sm-primary,.btn-sm-secondary,.btn-sm-outline,.btn-sm-ghost,.btn-sm-link,.btn-sm-destructive,.btn-lg,.btn-lg-primary,.btn-lg-secondary,.btn-lg-outline,.btn-lg-ghost,.btn-lg-link,.btn-lg-destructive,.btn-icon,.btn-icon-primary,.btn-icon-secondary,.btn-icon-outline,.btn-icon-ghost,.btn-icon-link,.btn-icon-destructive,.btn-sm-icon,.btn-sm-icon-primary,.btn-sm-icon-secondary,.btn-sm-icon-outline,.btn-sm-icon-ghost,.btn-sm-icon-link,.btn-sm-icon-destructive,.btn-lg-icon,.btn-lg-icon-primary,.btn-lg-icon-secondary,.btn-lg-icon-outline,.btn-lg-icon-ghost,.btn-lg-icon-link,.btn-lg-icon-destructive):disabled{pointer-events:none;opacity:.5}:is(.btn,.btn-primary,.btn-secondary,.btn-outline,.btn-ghost,.btn-link,.btn-destructive,.btn-sm,.btn-sm-primary,.btn-sm-secondary,.btn-sm-outline,.btn-sm-ghost,.btn-sm-link,.btn-sm-destructive,.btn-lg,.btn-lg-primary,.btn-lg-secondary,.btn-lg-outline,.btn-lg-ghost,.btn-lg-link,.btn-lg-destructive,.btn-icon,.btn-icon-primary,.btn-icon-secondary,.btn-icon-outline,.btn-icon-ghost,.btn-icon-link,.btn-icon-destructive,.btn-sm-icon,.btn-sm-icon-primary,.btn-sm-icon-secondary,.btn-sm-icon-outline,.btn-sm-icon-ghost,.btn-sm-icon-link,.btn-sm-icon-destructive,.btn-lg-icon,.btn-lg-icon-primary,.btn-lg-icon-secondary,.btn-lg-icon-outline,.btn-lg-icon-ghost,.btn-lg-icon-link,.btn-lg-icon-destructive)[aria-invalid=true]{border-color:var(--color-destructive);--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.btn,.btn-primary,.btn-secondary,.btn-outline,.btn-ghost,.btn-link,.btn-destructive,.btn-sm,.btn-sm-primary,.btn-sm-secondary,.btn-sm-outline,.btn-sm-ghost,.btn-sm-link,.btn-sm-destructive,.btn-lg,.btn-lg-primary,.btn-lg-secondary,.btn-lg-outline,.btn-lg-ghost,.btn-lg-link,.btn-lg-destructive,.btn-icon,.btn-icon-primary,.btn-icon-secondary,.btn-icon-outline,.btn-icon-ghost,.btn-icon-link,.btn-icon-destructive,.btn-sm-icon,.btn-sm-icon-primary,.btn-sm-icon-secondary,.btn-sm-icon-outline,.btn-sm-icon-ghost,.btn-sm-icon-link,.btn-sm-icon-destructive,.btn-lg-icon,.btn-lg-icon-primary,.btn-lg-icon-secondary,.btn-lg-icon-outline,.btn-lg-icon-ghost,.btn-lg-icon-link,.btn-lg-icon-destructive)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)20%,transparent)}}:is(.btn,.btn-primary,.btn-secondary,.btn-outline,.btn-ghost,.btn-link,.btn-destructive,.btn-sm,.btn-sm-primary,.btn-sm-secondary,.btn-sm-outline,.btn-sm-ghost,.btn-sm-link,.btn-sm-destructive,.btn-lg,.btn-lg-primary,.btn-lg-secondary,.btn-lg-outline,.btn-lg-ghost,.btn-lg-link,.btn-lg-destructive,.btn-icon,.btn-icon-primary,.btn-icon-secondary,.btn-icon-outline,.btn-icon-ghost,.btn-icon-link,.btn-icon-destructive,.btn-sm-icon,.btn-sm-icon-primary,.btn-sm-icon-secondary,.btn-sm-icon-outline,.btn-sm-icon-ghost,.btn-sm-icon-link,.btn-sm-icon-destructive,.btn-lg-icon,.btn-lg-icon-primary,.btn-lg-icon-secondary,.btn-lg-icon-outline,.btn-lg-icon-ghost,.btn-lg-icon-link,.btn-lg-icon-destructive):is(.dark *)[aria-invalid=true]{--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.btn,.btn-primary,.btn-secondary,.btn-outline,.btn-ghost,.btn-link,.btn-destructive,.btn-sm,.btn-sm-primary,.btn-sm-secondary,.btn-sm-outline,.btn-sm-ghost,.btn-sm-link,.btn-sm-destructive,.btn-lg,.btn-lg-primary,.btn-lg-secondary,.btn-lg-outline,.btn-lg-ghost,.btn-lg-link,.btn-lg-destructive,.btn-icon,.btn-icon-primary,.btn-icon-secondary,.btn-icon-outline,.btn-icon-ghost,.btn-icon-link,.btn-icon-destructive,.btn-sm-icon,.btn-sm-icon-primary,.btn-sm-icon-secondary,.btn-sm-icon-outline,.btn-sm-icon-ghost,.btn-sm-icon-link,.btn-sm-icon-destructive,.btn-lg-icon,.btn-lg-icon-primary,.btn-lg-icon-secondary,.btn-lg-icon-outline,.btn-lg-icon-ghost,.btn-lg-icon-link,.btn-lg-icon-destructive):is(.dark *)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)40%,transparent)}}:is(.btn,.btn-primary,.btn-secondary,.btn-outline,.btn-ghost,.btn-link,.btn-destructive,.btn-sm,.btn-sm-primary,.btn-sm-secondary,.btn-sm-outline,.btn-sm-ghost,.btn-sm-link,.btn-sm-destructive,.btn-lg,.btn-lg-primary,.btn-lg-secondary,.btn-lg-outline,.btn-lg-ghost,.btn-lg-link,.btn-lg-destructive,.btn-icon,.btn-icon-primary,.btn-icon-secondary,.btn-icon-outline,.btn-icon-ghost,.btn-icon-link,.btn-icon-destructive,.btn-sm-icon,.btn-sm-icon-primary,.btn-sm-icon-secondary,.btn-sm-icon-outline,.btn-sm-icon-ghost,.btn-sm-icon-link,.btn-sm-icon-destructive,.btn-lg-icon,.btn-lg-icon-primary,.btn-lg-icon-secondary,.btn-lg-icon-outline,.btn-lg-icon-ghost,.btn-lg-icon-link,.btn-lg-icon-destructive) svg{pointer-events:none;flex-shrink:0}:is(.btn,.btn-primary,.btn-secondary,.btn-outline,.btn-ghost,.btn-link,.btn-destructive,.btn-sm,.btn-sm-primary,.btn-sm-secondary,.btn-sm-outline,.btn-sm-ghost,.btn-sm-link,.btn-sm-destructive,.btn-lg,.btn-lg-primary,.btn-lg-secondary,.btn-lg-outline,.btn-lg-ghost,.btn-lg-link,.btn-lg-destructive,.btn-icon,.btn-icon-primary,.btn-icon-secondary,.btn-icon-outline,.btn-icon-ghost,.btn-icon-link,.btn-icon-destructive,.btn-sm-icon,.btn-sm-icon-primary,.btn-sm-icon-secondary,.btn-sm-icon-outline,.btn-sm-icon-ghost,.btn-sm-icon-link,.btn-sm-icon-destructive,.btn-lg-icon,.btn-lg-icon-primary,.btn-lg-icon-secondary,.btn-lg-icon-outline,.btn-lg-icon-ghost,.btn-lg-icon-link,.btn-lg-icon-destructive) svg:not([class*=size-]){width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.btn,.btn-primary,.btn-secondary,.btn-outline,.btn-ghost,.btn-link,.btn-destructive{height:calc(var(--spacing)*9);gap:calc(var(--spacing)*2);padding-inline:calc(var(--spacing)*4);padding-block:calc(var(--spacing)*2)}:is(.btn,.btn-primary,.btn-secondary,.btn-outline,.btn-ghost,.btn-link,.btn-destructive):has(>svg){padding-inline:calc(var(--spacing)*3)}.btn-icon,.btn-icon-primary,.btn-icon-secondary,.btn-icon-outline,.btn-icon-ghost,.btn-icon-link,.btn-icon-destructive{width:calc(var(--spacing)*9);height:calc(var(--spacing)*9)}.btn-sm,.btn-sm-primary,.btn-sm-secondary,.btn-sm-outline,.btn-sm-ghost,.btn-sm-link,.btn-sm-destructive{height:calc(var(--spacing)*8);gap:calc(var(--spacing)*1.5);padding-inline:calc(var(--spacing)*3)}:is(.btn-sm,.btn-sm-primary,.btn-sm-secondary,.btn-sm-outline,.btn-sm-ghost,.btn-sm-link,.btn-sm-destructive):has(>svg){padding-inline:calc(var(--spacing)*2.5)}.btn-sm-icon,.btn-sm-icon-primary,.btn-sm-icon-secondary,.btn-sm-icon-outline,.btn-sm-icon-ghost,.btn-sm-icon-link,.btn-sm-icon-destructive{width:calc(var(--spacing)*8);height:calc(var(--spacing)*8)}.btn-lg,.btn-lg-primary,.btn-lg-secondary,.btn-lg-outline,.btn-lg-ghost,.btn-lg-link,.btn-lg-destructive{height:calc(var(--spacing)*10);gap:calc(var(--spacing)*2);padding-inline:calc(var(--spacing)*6)}:is(.btn-lg,.btn-lg-primary,.btn-lg-secondary,.btn-lg-outline,.btn-lg-ghost,.btn-lg-link,.btn-lg-destructive):has(>svg){padding-inline:calc(var(--spacing)*4)}.btn-lg-icon,.btn-lg-icon-primary,.btn-lg-icon-secondary,.btn-lg-icon-outline,.btn-lg-icon-ghost,.btn-lg-icon-link,.btn-lg-icon-destructive{width:calc(var(--spacing)*10);height:calc(var(--spacing)*10)}.btn,.btn-primary,.btn-sm,.btn-sm-primary,.btn-lg,.btn-lg-primary,.btn-icon,.btn-icon-primary,.btn-sm-icon,.btn-sm-icon-primary,.btn-lg-icon,.btn-lg-icon-primary{background-color:var(--color-primary);color:var(--color-primary-foreground);--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){:is(.btn,.btn-primary,.btn-sm,.btn-sm-primary,.btn-lg,.btn-lg-primary,.btn-icon,.btn-icon-primary,.btn-sm-icon,.btn-sm-icon-primary,.btn-lg-icon,.btn-lg-icon-primary):hover{background-color:var(--color-primary)}@supports (color:color-mix(in lab, red, red)){:is(.btn,.btn-primary,.btn-sm,.btn-sm-primary,.btn-lg,.btn-lg-primary,.btn-icon,.btn-icon-primary,.btn-sm-icon,.btn-sm-icon-primary,.btn-lg-icon,.btn-lg-icon-primary):hover{background-color:color-mix(in oklab,var(--color-primary)90%,transparent)}}}:is(.btn,.btn-primary,.btn-sm,.btn-sm-primary,.btn-lg,.btn-lg-primary,.btn-icon,.btn-icon-primary,.btn-sm-icon,.btn-sm-icon-primary,.btn-lg-icon,.btn-lg-icon-primary)[aria-pressed=true]{background-color:var(--color-primary)}@supports (color:color-mix(in lab, red, red)){:is(.btn,.btn-primary,.btn-sm,.btn-sm-primary,.btn-lg,.btn-lg-primary,.btn-icon,.btn-icon-primary,.btn-sm-icon,.btn-sm-icon-primary,.btn-lg-icon,.btn-lg-icon-primary)[aria-pressed=true]{background-color:color-mix(in oklab,var(--color-primary)90%,transparent)}}.btn-secondary,.btn-sm-secondary,.btn-lg-secondary,.btn-icon-secondary,.btn-sm-icon-secondary,.btn-lg-icon-secondary{background-color:var(--color-secondary);color:var(--color-secondary-foreground);--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}:is(.btn-secondary,.btn-sm-secondary,.btn-lg-secondary,.btn-icon-secondary,.btn-sm-icon-secondary,.btn-lg-icon-secondary):hover,:is(.btn-secondary,.btn-sm-secondary,.btn-lg-secondary,.btn-icon-secondary,.btn-sm-icon-secondary,.btn-lg-icon-secondary)[aria-pressed=true]{background-color:var(--color-secondary)}@supports (color:color-mix(in lab, red, red)){:is(:is(.btn-secondary,.btn-sm-secondary,.btn-lg-secondary,.btn-icon-secondary,.btn-sm-icon-secondary,.btn-lg-icon-secondary):hover,:is(.btn-secondary,.btn-sm-secondary,.btn-lg-secondary,.btn-icon-secondary,.btn-sm-icon-secondary,.btn-lg-icon-secondary)[aria-pressed=true]){background-color:color-mix(in oklab,var(--color-secondary)80%,transparent)}}.btn-outline,.btn-sm-outline,.btn-lg-outline,.btn-icon-outline,.btn-sm-icon-outline,.btn-lg-icon-outline{border-style:var(--tw-border-style);background-color:var(--color-background);--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);border-width:1px}:is(.btn-outline,.btn-sm-outline,.btn-lg-outline,.btn-icon-outline,.btn-sm-icon-outline,.btn-lg-icon-outline):is(.dark *){border-color:var(--color-input);background-color:var(--color-input)}@supports (color:color-mix(in lab, red, red)){:is(.btn-outline,.btn-sm-outline,.btn-lg-outline,.btn-icon-outline,.btn-sm-icon-outline,.btn-lg-icon-outline):is(.dark *){background-color:color-mix(in oklab,var(--color-input)30%,transparent)}}:is(.btn-outline,.btn-sm-outline,.btn-lg-outline,.btn-icon-outline,.btn-sm-icon-outline,.btn-lg-icon-outline):hover,:is(.btn-outline,.btn-sm-outline,.btn-lg-outline,.btn-icon-outline,.btn-sm-icon-outline,.btn-lg-icon-outline)[aria-pressed=true]{background-color:var(--color-accent);color:var(--color-accent-foreground)}:is(:is(.btn-outline,.btn-sm-outline,.btn-lg-outline,.btn-icon-outline,.btn-sm-icon-outline,.btn-lg-icon-outline):hover,:is(.btn-outline,.btn-sm-outline,.btn-lg-outline,.btn-icon-outline,.btn-sm-icon-outline,.btn-lg-icon-outline)[aria-pressed=true]):is(.dark *){background-color:var(--color-accent)}@supports (color:color-mix(in lab, red, red)){:is(:is(.btn-outline,.btn-sm-outline,.btn-lg-outline,.btn-icon-outline,.btn-sm-icon-outline,.btn-lg-icon-outline):hover,:is(.btn-outline,.btn-sm-outline,.btn-lg-outline,.btn-icon-outline,.btn-sm-icon-outline,.btn-lg-icon-outline)[aria-pressed=true]):is(.dark *){background-color:color-mix(in oklab,var(--color-accent)50%,transparent)}}:is(.btn-ghost,.btn-sm-ghost,.btn-lg-ghost,.btn-icon-ghost,.btn-sm-icon-ghost,.btn-lg-icon-ghost):hover,:is(.btn-ghost,.btn-sm-ghost,.btn-lg-ghost,.btn-icon-ghost,.btn-sm-icon-ghost,.btn-lg-icon-ghost)[aria-pressed=true]{background-color:var(--color-accent);color:var(--color-accent-foreground)}:is(:is(.btn-ghost,.btn-sm-ghost,.btn-lg-ghost,.btn-icon-ghost,.btn-sm-icon-ghost,.btn-lg-icon-ghost):hover,:is(.btn-ghost,.btn-sm-ghost,.btn-lg-ghost,.btn-icon-ghost,.btn-sm-icon-ghost,.btn-lg-icon-ghost)[aria-pressed=true]):is(.dark *){background-color:var(--color-accent)}@supports (color:color-mix(in lab, red, red)){:is(:is(.btn-ghost,.btn-sm-ghost,.btn-lg-ghost,.btn-icon-ghost,.btn-sm-icon-ghost,.btn-lg-icon-ghost):hover,:is(.btn-ghost,.btn-sm-ghost,.btn-lg-ghost,.btn-icon-ghost,.btn-sm-icon-ghost,.btn-lg-icon-ghost)[aria-pressed=true]):is(.dark *){background-color:color-mix(in oklab,var(--color-accent)50%,transparent)}}.btn-link,.btn-sm-link,.btn-lg-link,.btn-icon-link,.btn-sm-icon-link,.btn-lg-icon-link{color:var(--color-primary);text-underline-offset:4px}@media (hover:hover){:is(:is(.btn-link,.btn-sm-link,.btn-lg-link,.btn-icon-link,.btn-sm-icon-link,.btn-lg-icon-link):hover,:is(.btn-link,.btn-sm-link,.btn-lg-link,.btn-icon-link,.btn-sm-icon-link,.btn-lg-icon-link)[aria-pressed=true]):hover{text-decoration-line:underline}}.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive{background-color:var(--color-destructive);color:var(--color-white);--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive):focus-visible{--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive):focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-destructive)20%,transparent)}}:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive):is(.dark *){background-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive):is(.dark *){background-color:color-mix(in oklab,var(--color-destructive)60%,transparent)}}:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive):is(.dark *):focus-visible{--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive):is(.dark *):focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-destructive)40%,transparent)}}:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive):hover,:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive)[aria-pressed=true]{background-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive):hover,:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive)[aria-pressed=true]){background-color:color-mix(in oklab,var(--color-destructive)90%,transparent)}}:is(:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive):hover,:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive)[aria-pressed=true]):is(.dark *){background-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive):hover,:is(.btn-destructive,.btn-sm-destructive,.btn-lg-destructive,.btn-icon-destructive,.btn-sm-icon-destructive,.btn-lg-icon-destructive)[aria-pressed=true]):is(.dark *){background-color:color-mix(in oklab,var(--color-destructive)50%,transparent)}}.badge,.badge-primary,.badge-secondary,.badge-destructive,.badge-outline{justify-content:center;align-items:center;gap:calc(var(--spacing)*1);border-radius:var(--radius-md);border-style:var(--tw-border-style);width:fit-content;padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*.5);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);white-space:nowrap;transition-property:color,box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));border-width:1px;flex-shrink:0;display:inline-flex;overflow:hidden}:is(.badge,.badge-primary,.badge-secondary,.badge-destructive,.badge-outline):focus-visible{border-color:var(--color-ring);--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring)}@supports (color:color-mix(in lab, red, red)){:is(.badge,.badge-primary,.badge-secondary,.badge-destructive,.badge-outline):focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}:is(.badge,.badge-primary,.badge-secondary,.badge-destructive,.badge-outline)[aria-invalid=true]{border-color:var(--color-destructive);--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.badge,.badge-primary,.badge-secondary,.badge-destructive,.badge-outline)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)20%,transparent)}}:is(.badge,.badge-primary,.badge-secondary,.badge-destructive,.badge-outline):is(.dark *)[aria-invalid=true]{--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.badge,.badge-primary,.badge-secondary,.badge-destructive,.badge-outline):is(.dark *)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)40%,transparent)}}:is(.badge,.badge-primary,.badge-secondary,.badge-destructive,.badge-outline)>svg{pointer-events:none;width:calc(var(--spacing)*3);height:calc(var(--spacing)*3)}.badge,.badge-primary{background-color:var(--color-primary);color:var(--color-primary-foreground);border-color:#0000}@media (hover:hover){a:is(.badge,.badge-primary):hover{background-color:var(--color-primary)}@supports (color:color-mix(in lab, red, red)){a:is(.badge,.badge-primary):hover{background-color:color-mix(in oklab,var(--color-primary)90%,transparent)}}}.badge-secondary{background-color:var(--color-secondary);color:var(--color-secondary-foreground);border-color:#0000}@media (hover:hover){a.badge-secondary:hover{background-color:var(--color-secondary)}@supports (color:color-mix(in lab, red, red)){a.badge-secondary:hover{background-color:color-mix(in oklab,var(--color-secondary)90%,transparent)}}}.badge-destructive{background-color:var(--color-destructive);color:var(--color-white);border-color:#0000}.badge-destructive:focus-visible{--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){.badge-destructive:focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-destructive)20%,transparent)}}.badge-destructive:is(.dark *){background-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){.badge-destructive:is(.dark *){background-color:color-mix(in oklab,var(--color-destructive)60%,transparent)}}.badge-destructive:is(.dark *):focus-visible{--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){.badge-destructive:is(.dark *):focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-destructive)40%,transparent)}}@media (hover:hover){a.badge-destructive:hover{background-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){a.badge-destructive:hover{background-color:color-mix(in oklab,var(--color-destructive)90%,transparent)}}}.badge-outline{color:var(--color-foreground)}@media (hover:hover){a.badge-outline:hover{background-color:var(--color-accent);color:var(--color-accent-foreground)}}.card{gap:calc(var(--spacing)*6);border-radius:var(--radius-xl);border-style:var(--tw-border-style);background-color:var(--color-card);padding-block:calc(var(--spacing)*6);color:var(--color-card-foreground);--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);border-width:1px;flex-direction:column;display:flex}.card>header{align-items:flex-start;gap:calc(var(--spacing)*1.5);padding-inline:calc(var(--spacing)*6);grid-template-rows:auto auto;grid-auto-rows:min-content;display:grid;container:card-header/inline-size}.card>header:has([data-slot=card-action]){grid-template-columns:1fr auto}.card>header.border-b{padding-bottom:calc(var(--spacing)*6)}.card>header h2{--tw-leading:1;--tw-font-weight:var(--font-weight-semibold);line-height:1;font-weight:var(--font-weight-semibold)}.card>header p{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));color:var(--color-muted-foreground)}.card>section{padding-inline:calc(var(--spacing)*6)}.card>footer{padding-inline:calc(var(--spacing)*6);align-items:center;display:flex}.card>footer.border-t{padding-top:calc(var(--spacing)*6)}.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch]){width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);appearance:none;border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-input);--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);transition-property:box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-outline-style:none;border-radius:4px;outline-style:none;flex-shrink:0}:is(.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch])):checked{border-color:var(--color-primary);background-color:var(--color-primary)}:is(.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch])):focus-visible{border-color:var(--color-ring);--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch])):focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}:is(.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch])):disabled{cursor:not-allowed;opacity:.5}:is(.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch]))[aria-invalid=true]{border-color:var(--color-destructive);--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch]))[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)20%,transparent)}}:is(.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch])):is(.dark *){background-color:var(--color-input)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch])):is(.dark *){background-color:color-mix(in oklab,var(--color-input)30%,transparent)}}:is(.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch])):is(.dark *):checked{background-color:var(--color-primary)}:is(.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch])):is(.dark *)[aria-invalid=true]{--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch])):is(.dark *)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)40%,transparent)}}:is(.form input[type=checkbox]:not([role=switch]),.input[type=checkbox]:not([role=switch])):checked:after{width:calc(var(--spacing)*3.5);height:calc(var(--spacing)*3.5);background-color:var(--color-primary-foreground);--tw-content:"";content:var(--tw-content);-webkit-mask-image:var(--check-icon);-webkit-mask-image:var(--check-icon);mask-image:var(--check-icon);display:block;-webkit-mask-position:50%;mask-position:50%;-webkit-mask-size:.875rem;mask-size:.875rem;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}details::details-content{opacity:0;block-size:0;transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));transition-behavior:allow-discrete;display:block}details[open]::details-content{block-size:auto;block-size:calc-size(auto,size);opacity:1}details summary{cursor:pointer;align-items:center;display:inline-flex}details>summary::-webkit-details-marker{display:none}.dialog{inset-block:calc(var(--spacing)*0);opacity:0;transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));transition-behavior:allow-discrete}.dialog:is([open],:popover-open),.dialog:is([open],:popover-open)::backdrop{opacity:1}.dialog:is([open],:popover-open)>article{--tw-scale-x:100%;--tw-scale-y:100%;--tw-scale-z:100%;scale:var(--tw-scale-x)var(--tw-scale-y)}@starting-style{.dialog:is([open],:popover-open),.dialog:is([open],:popover-open)::backdrop{opacity:0}.dialog:is([open],:popover-open)>article{--tw-scale-x:95%;--tw-scale-y:95%;--tw-scale-z:95%;scale:var(--tw-scale-x)var(--tw-scale-y)}}.dialog::backdrop{opacity:0;transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));transition-behavior:allow-discrete;background-color:#00000080}@supports (color:color-mix(in lab, red, red)){.dialog::backdrop{background-color:color-mix(in oklab,var(--color-black)50%,transparent)}}.dialog>article{z-index:50;--tw-translate-x:calc(calc(1/2*100%)*-1);width:100%;max-width:calc(100% - 2rem);max-height:calc(100% - 2rem);translate:var(--tw-translate-x)var(--tw-translate-y);--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y);gap:calc(var(--spacing)*4);border-radius:var(--radius-lg);border-style:var(--tw-border-style);background-color:var(--color-background);padding:calc(var(--spacing)*6);--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-scale-x:95%;--tw-scale-y:95%;--tw-scale-z:95%;scale:var(--tw-scale-x)var(--tw-scale-y);transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));border-width:1px;flex-direction:column;display:flex;position:fixed;top:50%;left:50%}@media (min-width:40rem){.dialog>article{max-width:var(--container-lg)}}.dialog>article>header{gap:calc(var(--spacing)*2);text-align:center;flex-direction:column;display:flex}@media (min-width:40rem){.dialog>article>header{text-align:left}}.dialog>article>header>h2{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height));--tw-leading:1;--tw-font-weight:var(--font-weight-semibold);line-height:1;font-weight:var(--font-weight-semibold)}.dialog>article>header>p{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));color:var(--color-muted-foreground)}.dialog>article>section{margin-inline:calc(var(--spacing)*-6);padding-inline:calc(var(--spacing)*6);flex:1}.dialog>article>footer{gap:calc(var(--spacing)*2);flex-direction:column-reverse;display:flex}@media (min-width:40rem){.dialog>article>footer{flex-direction:row;justify-content:flex-end}}.dialog>article>form[method=dialog]{top:calc(var(--spacing)*4);right:calc(var(--spacing)*4);position:absolute}.dialog>article>form[method=dialog]>button{border-radius:var(--radius-xs);opacity:.7;--tw-ring-offset-color:var(--color-background);transition-property:opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}@media (hover:hover){.dialog>article>form[method=dialog]>button:hover{opacity:1}}.dialog>article>form[method=dialog]>button:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring);--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color);--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.dialog>article>form[method=dialog]>button:focus{outline-offset:2px;outline:2px solid #0000}}.dialog>article>form[method=dialog]>button:disabled{pointer-events:none}.dialog>article>form[method=dialog]>button[data-state=open]{background-color:var(--color-accent);color:var(--color-muted-foreground)}.dialog>article>form[method=dialog]>button svg{pointer-events:none;flex-shrink:0}.dialog>article>form[method=dialog]>button svg:not([class*=size-]){width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.dropdown-menu{display:inline-flex;position:relative}.dropdown-menu [data-popover]{padding:calc(var(--spacing)*1);min-width:anchor-size(width)}.dropdown-menu [data-popover] [role=menuitem],.dropdown-menu [data-popover] [role=menuitemcheckbox],.dropdown-menu [data-popover] [role=menuitemradio]{cursor:default;align-items:center;gap:calc(var(--spacing)*2);text-overflow:ellipsis;white-space:nowrap;border-radius:var(--radius-sm);width:100%;padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*1.5);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-outline-style:none;-webkit-user-select:none;user-select:none;outline-style:none;display:flex;position:relative;overflow:hidden}@media (forced-colors:active){:is(.dropdown-menu [data-popover] [role=menuitem],.dropdown-menu [data-popover] [role=menuitemcheckbox],.dropdown-menu [data-popover] [role=menuitemradio]){outline-offset:2px;outline:2px solid #0000}}:is(.dropdown-menu [data-popover] [role=menuitem],.dropdown-menu [data-popover] [role=menuitemcheckbox],.dropdown-menu [data-popover] [role=menuitemradio]):disabled,:is(.dropdown-menu [data-popover] [role=menuitem],.dropdown-menu [data-popover] [role=menuitemcheckbox],.dropdown-menu [data-popover] [role=menuitemradio])[aria-disabled=true]{pointer-events:none;opacity:.5}:is(.dropdown-menu [data-popover] [role=menuitem],.dropdown-menu [data-popover] [role=menuitemcheckbox],.dropdown-menu [data-popover] [role=menuitemradio])[aria-hidden=true]{display:none}:is(.dropdown-menu [data-popover] [role=menuitem],.dropdown-menu [data-popover] [role=menuitemcheckbox],.dropdown-menu [data-popover] [role=menuitemradio]) svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);color:var(--color-muted-foreground);flex-shrink:0}:is(.dropdown-menu [data-popover] [role=menuitem],.dropdown-menu [data-popover] [role=menuitemcheckbox],.dropdown-menu [data-popover] [role=menuitemradio]):not([aria-disabled=true]):focus-visible,:is(.dropdown-menu [data-popover] [role=menuitem],.dropdown-menu [data-popover] [role=menuitemcheckbox],.dropdown-menu [data-popover] [role=menuitemradio]).active{background-color:var(--color-accent);color:var(--color-accent-foreground)}.dropdown-menu [data-popover] [role=menu] [role=heading]{padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*1.5);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);display:flex}.dropdown-menu [data-popover] [role=separator]{margin-inline:calc(var(--spacing)*-1);margin-block:calc(var(--spacing)*1);border-color:var(--color-border)}@media (hover:hover){:is(.dropdown-menu:not([data-dropdown-menu-initialized]) [data-popover] [role=menuitem],.dropdown-menu:not([data-dropdown-menu-initialized]) [data-popover] [role=menuitemcheckbox],.dropdown-menu:not([data-dropdown-menu-initialized]) [data-popover] [role=menuitemradio]):hover{background-color:var(--color-accent);color:var(--color-accent-foreground)}}.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time]{height:calc(var(--spacing)*9);width:100%;min-width:calc(var(--spacing)*0);appearance:none;border-radius:var(--radius-md);border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-input);padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*1);font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height));--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);transition-property:color,box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-outline-style:none;background-color:#0000;outline-style:none;display:flex}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time]) ::selection{background-color:var(--color-primary)}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time])::selection{background-color:var(--color-primary)}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time]) ::selection{color:var(--color-primary-foreground)}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time])::selection{color:var(--color-primary-foreground)}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time])::file-selector-button{display:inline-flex}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time])::file-selector-button{height:calc(var(--spacing)*7)}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time])::file-selector-button{border-style:var(--tw-border-style);border-width:0}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time])::file-selector-button{background-color:#0000}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time])::file-selector-button{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time])::file-selector-button{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time])::file-selector-button{color:var(--color-foreground)}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time])::placeholder{color:var(--color-muted-foreground)}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time]):disabled{pointer-events:none;cursor:not-allowed;opacity:.5}@media (min-width:48rem){:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time]){font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time]):is(.dark *){background-color:var(--color-input)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time]):is(.dark *){background-color:color-mix(in oklab,var(--color-input)30%,transparent)}}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time]):focus-visible{border-color:var(--color-ring);--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time]):focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time])[aria-invalid=true]{border-color:var(--color-destructive);--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time])[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)20%,transparent)}}:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time]):is(.dark *)[aria-invalid=true]{--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=text],.form input[type=email],.form input[type=password],.form input[type=number],.form input[type=file],.form input[type=tel],.form input[type=url],.form input[type=search],.form input[type=date],.form input[type=datetime-local],.form input[type=month],.form input[type=week],.form input[type=time],.input[type=text],.input[type=email],.input[type=password],.input[type=number],.input[type=file],.input[type=tel],.input[type=url],.input[type=search],.input[type=date],.input[type=datetime-local],.input[type=month],.input[type=week],.input[type=time]):is(.dark *)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)40%,transparent)}}.form label,.label{align-items:center;gap:calc(var(--spacing)*2);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-leading:1;--tw-font-weight:var(--font-weight-medium);line-height:1;font-weight:var(--font-weight-medium);-webkit-user-select:none;user-select:none;display:flex}:is(.form label,.label):is(:where(.peer):disabled~*),:is(.form label,.label):has(>:disabled),:is(.form label,.label):has(+:disabled){pointer-events:none;opacity:.5}[data-popover]{visibility:visible;z-index:50;--tw-scale-x:100%;--tw-scale-y:100%;--tw-scale-z:100%;width:max-content;min-width:100%;scale:var(--tw-scale-x)var(--tw-scale-y);border-radius:var(--radius-md);border-style:var(--tw-border-style);background-color:var(--color-popover);color:var(--color-popover-foreground);opacity:1;--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));border-width:1px;position:absolute;overflow:hidden auto}[data-popover][aria-hidden=true]{visibility:hidden;--tw-scale-x:95%;--tw-scale-y:95%;--tw-scale-z:95%;scale:var(--tw-scale-x)var(--tw-scale-y);opacity:0}[data-popover][aria-hidden=true]:not([data-side]),[data-popover][aria-hidden=true][data-side=bottom]{--tw-translate-y:calc(var(--spacing)*-2);translate:var(--tw-translate-x)var(--tw-translate-y)}[data-popover][aria-hidden=true][data-side=top]{--tw-translate-y:calc(var(--spacing)*2);translate:var(--tw-translate-x)var(--tw-translate-y)}[data-popover][aria-hidden=true][data-side=left]{--tw-translate-x:calc(var(--spacing)*2);translate:var(--tw-translate-x)var(--tw-translate-y)}[data-popover][aria-hidden=true][data-side=right]{--tw-translate-x:calc(var(--spacing)*-2);translate:var(--tw-translate-x)var(--tw-translate-y)}[data-popover]:not([data-side]),[data-popover][data-side=bottom]{margin-top:calc(var(--spacing)*1);top:100%}[data-popover][data-side=top]{margin-bottom:calc(var(--spacing)*1);bottom:100%}[data-popover][data-side=left]{margin-right:calc(var(--spacing)*1);right:100%}[data-popover][data-side=right]{margin-left:calc(var(--spacing)*1);left:100%}:is([data-popover]:not([data-side]),[data-popover][data-side=bottom],[data-popover][data-side=top]):not([data-align]),:is([data-popover]:not([data-side]),[data-popover][data-side=bottom],[data-popover][data-side=top])[data-align=start]{left:calc(var(--spacing)*0)}:is([data-popover]:not([data-side]),[data-popover][data-side=bottom],[data-popover][data-side=top])[data-align=end]{right:calc(var(--spacing)*0)}:is([data-popover]:not([data-side]),[data-popover][data-side=bottom],[data-popover][data-side=top])[data-align=center]{--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y);left:50%}:is([data-popover][data-side=left],[data-popover][data-side=right]):not([data-align]),:is([data-popover][data-side=left],[data-popover][data-side=right])[data-align=start]{top:calc(var(--spacing)*0)}:is([data-popover][data-side=left],[data-popover][data-side=right])[data-align=end]{bottom:calc(var(--spacing)*0)}:is([data-popover][data-side=left],[data-popover][data-side=right])[data-align=center]{--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y);top:50%}.popover{display:inline-flex;position:relative}.popover [data-popover]{padding:calc(var(--spacing)*4)}.form input[type=radio],.input[type=radio]{aspect-ratio:1;width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);appearance:none;border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-input);color:var(--color-primary);--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);transition-property:color,box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-outline-style:none;border-radius:3.40282e38px;outline-style:none;flex-shrink:0;position:relative}:is(.form input[type=radio],.input[type=radio]):focus-visible{border-color:var(--color-ring);--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=radio],.input[type=radio]):focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}:is(.form input[type=radio],.input[type=radio]):disabled{cursor:not-allowed;opacity:.5}:is(.form input[type=radio],.input[type=radio])[aria-invalid=true]{border-color:var(--color-destructive);--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=radio],.input[type=radio])[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)20%,transparent)}}:is(.form input[type=radio],.input[type=radio]):is(.dark *){background-color:var(--color-input)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=radio],.input[type=radio]):is(.dark *){background-color:color-mix(in oklab,var(--color-input)30%,transparent)}}:is(.form input[type=radio],.input[type=radio]):is(.dark *)[aria-invalid=true]{--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=radio],.input[type=radio]):is(.dark *)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)40%,transparent)}}:is(.form input[type=radio],.input[type=radio]):checked:before{width:calc(var(--spacing)*2);height:calc(var(--spacing)*2);--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y);--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y);background-color:var(--color-primary);--tw-content:"";content:var(--tw-content);border-radius:3.40282e38px;position:absolute;top:50%;left:50%}.form input[type=range],.input[type=range]{appearance:none;padding:calc(var(--spacing)*0);--tw-outline-style:none;--slider-value:20%;outline-style:none;align-items:center;display:flex}:is(:is(.form input[type=range],.input[type=range]):hover,:is(.form input[type=range],.input[type=range]):focus-visible)::-webkit-slider-thumb{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(4px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}:is(:is(.form input[type=range],.input[type=range]):hover,:is(.form input[type=range],.input[type=range]):focus-visible)::-moz-range-thumb{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(4px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}:is(:is(.form input[type=range],.input[type=range]):hover,:is(.form input[type=range],.input[type=range]):focus-visible)::-ms-thumb{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(4px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}:is(.form input[type=range],.input[type=range])::-webkit-slider-thumb{margin-top:calc(var(--spacing)*-1.25);width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);appearance:none;border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-primary);background-color:var(--color-background);--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring);border-radius:3.40282e38px;flex-shrink:0;display:block}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=range],.input[type=range])::-webkit-slider-thumb{--tw-ring-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}:is(.form input[type=range],.input[type=range])::-webkit-slider-runnable-track{height:calc(var(--spacing)*1.5);appearance:none;background:linear-gradient(to right,var(--primary)var(--slider-value),var(--muted)var(--slider-value));border-radius:3.40282e38px;width:100%}:is(.form input[type=range],.input[type=range])::-moz-range-thumb{margin-top:calc(var(--spacing)*-1.25);width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);appearance:none;border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-primary);background-color:var(--color-background);--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring);border-radius:3.40282e38px;flex-shrink:0;display:block}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=range],.input[type=range])::-moz-range-thumb{--tw-ring-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}:is(.form input[type=range],.input[type=range])::-moz-range-track{height:calc(var(--spacing)*1.5);appearance:none;background:linear-gradient(to right,var(--primary)var(--slider-value),var(--muted)var(--slider-value));border-radius:3.40282e38px;width:100%}:is(.form input[type=range],.input[type=range])::-ms-thumb{margin-top:calc(var(--spacing)*-1.25);width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);appearance:none;border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-primary);background-color:var(--color-background);--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring);border-radius:3.40282e38px;flex-shrink:0;display:block}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=range],.input[type=range])::-ms-thumb{--tw-ring-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}:is(.form input[type=range],.input[type=range])::-ms-track{height:calc(var(--spacing)*1.5);appearance:none;border-radius:3.40282e38px;width:100%}:is(.form input[type=range],.input[type=range])::-ms-fill-lower{background-color:var(--color-primary);border-radius:3.40282e38px}:is(.form input[type=range],.input[type=range])::-ms-fill-upper{background-color:var(--color-muted);border-radius:3.40282e38px}.form select,select.select{height:calc(var(--spacing)*9);appearance:none;justify-content:space-between;align-items:center;gap:calc(var(--spacing)*2);border-radius:var(--radius-md);border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-input);width:fit-content;padding-block:calc(var(--spacing)*2);padding-right:calc(var(--spacing)*9);padding-left:calc(var(--spacing)*3);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));white-space:nowrap;--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);transition-property:color,box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-outline-style:none;background-color:#0000;background-image:var(--chevron-down-icon-50);background-position:right .75rem center;background-repeat:no-repeat;background-size:1rem;outline-style:none;display:flex}:is(.form select,select.select):focus-visible{border-color:var(--color-ring);--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring)}@supports (color:color-mix(in lab, red, red)){:is(.form select,select.select):focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}:is(.form select,select.select):disabled{cursor:not-allowed;opacity:.5}:is(.form select,select.select)[aria-invalid=true]{border-color:var(--color-destructive);--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.form select,select.select)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)20%,transparent)}}:is(.form select,select.select):is(.dark *){background-color:var(--color-input)}@supports (color:color-mix(in lab, red, red)){:is(.form select,select.select):is(.dark *){background-color:color-mix(in oklab,var(--color-input)30%,transparent)}}@media (hover:hover){:is(.form select,select.select):is(.dark *):hover{background-color:var(--color-input)}@supports (color:color-mix(in lab, red, red)){:is(.form select,select.select):is(.dark *):hover{background-color:color-mix(in oklab,var(--color-input)50%,transparent)}}}:is(.form select,select.select):is(.dark *)[aria-invalid=true]{--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.form select,select.select):is(.dark *)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)40%,transparent)}}:is(.form select,select.select) option,:is(.form select,select.select) optgroup{background-color:var(--color-popover);color:var(--color-popover-foreground)}:not(select).select{display:inline-flex;position:relative}:not(select).select [data-popover]{padding:calc(var(--spacing)*1)}:not(select).select [data-popover] [role=option]{cursor:default;align-items:center;gap:calc(var(--spacing)*2);text-overflow:ellipsis;white-space:nowrap;border-radius:var(--radius-sm);width:100%;padding-block:calc(var(--spacing)*1.5);padding-right:calc(var(--spacing)*7.5);padding-left:calc(var(--spacing)*2);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-outline-style:none;-webkit-user-select:none;user-select:none;outline-style:none;display:flex;position:relative;overflow:hidden}@media (forced-colors:active){:not(select).select [data-popover] [role=option]{outline-offset:2px;outline:2px solid #0000}}:not(select).select [data-popover] [role=option]:disabled,:not(select).select [data-popover] [role=option][aria-disabled=true]{pointer-events:none;opacity:.5}:not(select).select [data-popover] [role=option][aria-hidden=true]{display:none}:not(select).select [data-popover] [role=option] svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);color:var(--color-muted-foreground);flex-shrink:0}:not(select).select [data-popover] [role=option][aria-selected=true]{background-image:var(--check-icon);background-position:right .5rem center;background-repeat:no-repeat;background-size:.875rem}:not(select).select [data-popover] [role=option].active,:not(select).select [data-popover] [role=option]:focus-visible{background-color:var(--color-accent);color:var(--color-accent-foreground)}:not(select).select [data-popover] [role=listbox] [role=heading]{padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*1.5);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));color:var(--color-muted-foreground);display:flex}:not(select).select [data-popover] [role=listbox] [role=group]:not(:has([role=option]:not([aria-hidden=true]))){display:none}:not(select).select [data-popover] [role=separator]{margin-inline:calc(var(--spacing)*-1);margin-block:calc(var(--spacing)*1);border-color:var(--color-border)}:not(select).select [data-popover]>header{margin-inline:calc(var(--spacing)*-1);margin-top:calc(var(--spacing)*-1);margin-bottom:calc(var(--spacing)*1);height:calc(var(--spacing)*9);align-items:center;gap:calc(var(--spacing)*2);border-bottom-style:var(--tw-border-style);padding-inline:calc(var(--spacing)*3);border-bottom-width:1px;display:flex}:not(select).select [data-popover]>header svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);opacity:.5;flex-shrink:0}:not(select).select [data-popover]>header input[role=combobox]{height:calc(var(--spacing)*10);width:100%;min-width:calc(var(--spacing)*0);border-radius:var(--radius-md);padding-block:calc(var(--spacing)*3);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-outline-style:none;background-color:#0000;outline-style:none;flex:1;display:flex}@media (forced-colors:active){:not(select).select [data-popover]>header input[role=combobox]{outline-offset:2px;outline:2px solid #0000}}:not(select).select [data-popover]>header input[role=combobox]::placeholder{color:var(--color-muted-foreground)}:not(select).select [data-popover]>header input[role=combobox]:disabled{cursor:not-allowed;opacity:.5}:not(select).select [data-popover] [role=listbox]:not(:has([data-value]:not([aria-hidden=true]))):before{text-overflow:ellipsis;white-space:nowrap;padding:calc(var(--spacing)*6);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));justify-content:center;align-items:center;display:flex;overflow:hidden}:not(select).select [data-popover] [role=listbox][data-empty]:not(:has([data-value]:not([aria-hidden=true]))):before{--tw-content:attr(data-empty);content:var(--tw-content)}:not(select).select [data-popover] [role=listbox]:not([data-empty]):not(:has([data-value]:not([aria-hidden=true]))):before{--tw-content:"No results found";content:var(--tw-content)}@media (hover:hover){:not(select).select:not([data-select-initialized]) [data-popover] [role=option]:hover{background-color:var(--color-accent);color:var(--color-accent-foreground)}}.form input[type=checkbox][role=switch],.input[type=checkbox][role=switch]{height:1.15rem;width:calc(var(--spacing)*8);appearance:none;border-style:var(--tw-border-style);--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-outline-style:none;background-color:var(--color-input);border-width:1px;border-color:#0000;border-radius:3.40282e38px;outline-style:none;flex-shrink:0;align-items:center;display:inline-flex}:is(.form input[type=checkbox][role=switch],.input[type=checkbox][role=switch]):focus-visible{border-color:var(--color-ring);--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=checkbox][role=switch],.input[type=checkbox][role=switch]):focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}:is(.form input[type=checkbox][role=switch],.input[type=checkbox][role=switch]):disabled{cursor:not-allowed;opacity:.5}:is(.form input[type=checkbox][role=switch],.input[type=checkbox][role=switch]):checked{background-color:var(--color-primary)}:is(.form input[type=checkbox][role=switch],.input[type=checkbox][role=switch]):is(.dark *){background-color:var(--color-input)}@supports (color:color-mix(in lab, red, red)){:is(.form input[type=checkbox][role=switch],.input[type=checkbox][role=switch]):is(.dark *){background-color:color-mix(in oklab,var(--color-input)80%,transparent)}}:is(.form input[type=checkbox][role=switch],.input[type=checkbox][role=switch]):is(.dark *):checked{background-color:var(--color-primary)}:is(.form input[type=checkbox][role=switch],.input[type=checkbox][role=switch]):before{content:var(--tw-content);pointer-events:none;content:var(--tw-content);content:var(--tw-content);width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);content:var(--tw-content);content:var(--tw-content);--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);content:var(--tw-content);transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));content:var(--tw-content);--tw-content:"";content:var(--tw-content);content:var(--tw-content);background-color:var(--color-background);border-radius:3.40282e38px;display:block}:is(.form input[type=checkbox][role=switch],.input[type=checkbox][role=switch]):is(.dark *):before{content:var(--tw-content);background-color:var(--color-foreground)}:is(.form input[type=checkbox][role=switch],.input[type=checkbox][role=switch]):checked:before{content:var(--tw-content);margin-inline-start:calc(var(--spacing)*3.5)}:is(.form input[type=checkbox][role=switch],.input[type=checkbox][role=switch]):is(.dark *):checked:before{content:var(--tw-content);background-color:var(--color-primary-foreground)}.table{caption-side:bottom;width:100%;font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.table thead tr{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.table tbody tr:last-child{border-style:var(--tw-border-style);border-width:0}.table tfoot{border-top-style:var(--tw-border-style);background-color:var(--color-muted);--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);border-top-width:1px}@supports (color:color-mix(in lab, red, red)){.table tfoot{background-color:color-mix(in oklab,var(--color-muted)50%,transparent)}}.table tfoot>tr:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.table tr{border-bottom-style:var(--tw-border-style);transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));border-bottom-width:1px}@media (hover:hover){.table tr:hover{background-color:var(--color-muted)}@supports (color:color-mix(in lab, red, red)){.table tr:hover{background-color:color-mix(in oklab,var(--color-muted)50%,transparent)}}}.table th{height:calc(var(--spacing)*10);padding-inline:calc(var(--spacing)*2);text-align:left;vertical-align:middle;--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);white-space:nowrap;color:var(--color-foreground)}.table th:has([role=checkbox]){padding-right:calc(var(--spacing)*0)}.table th>[role=checkbox]{--tw-translate-y:2px;translate:var(--tw-translate-x)var(--tw-translate-y)}.table td{padding:calc(var(--spacing)*2);vertical-align:middle;white-space:nowrap}.table td:has([role=checkbox]){padding-right:calc(var(--spacing)*0)}.table td>[role=checkbox]{--tw-translate-y:2px;translate:var(--tw-translate-x)var(--tw-translate-y)}.table caption{margin-top:calc(var(--spacing)*4);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));color:var(--color-muted-foreground)}.tabs{gap:calc(var(--spacing)*2);flex-direction:column;display:flex}.tabs [role=tablist]{height:calc(var(--spacing)*9);border-radius:var(--radius-lg);background-color:var(--color-muted);width:fit-content;color:var(--color-muted-foreground);justify-content:center;align-items:center;padding:3px;display:inline-flex}.tabs [role=tablist] [role=tab]{justify-content:center;align-items:center;gap:calc(var(--spacing)*1.5);border-radius:var(--radius-md);border-style:var(--tw-border-style);height:calc(100% - 1px);padding-inline:calc(var(--spacing)*2);padding-block:calc(var(--spacing)*1);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);white-space:nowrap;color:var(--color-foreground);transition-property:color,box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));border-width:1px;border-color:#0000;flex:1;display:inline-flex}.tabs [role=tablist] [role=tab]:focus-visible{border-color:var(--color-ring);--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring)}@supports (color:color-mix(in lab, red, red)){.tabs [role=tablist] [role=tab]:focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}.tabs [role=tablist] [role=tab]:focus-visible{outline-style:var(--tw-outline-style);outline-width:1px;outline-color:var(--color-ring)}.tabs [role=tablist] [role=tab]:disabled{pointer-events:none;opacity:.5}.tabs [role=tablist] [role=tab]:is(.dark *){color:var(--color-muted-foreground)}.tabs [role=tablist] [role=tab] svg{pointer-events:none;flex-shrink:0}.tabs [role=tablist] [role=tab] svg:not([class*=size-]){width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.tabs [role=tablist] [role=tab][aria-selected=true]{background-color:var(--color-background);--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.tabs [role=tablist] [role=tab][aria-selected=true]:is(.dark *){border-color:var(--color-input);background-color:var(--color-input)}@supports (color:color-mix(in lab, red, red)){.tabs [role=tablist] [role=tab][aria-selected=true]:is(.dark *){background-color:color-mix(in oklab,var(--color-input)30%,transparent)}}.tabs [role=tablist] [role=tab][aria-selected=true]:is(.dark *){color:var(--color-foreground)}.tabs [role=tabpanel]{--tw-outline-style:none;outline-style:none;flex:1}.form textarea,.textarea{field-sizing:content;min-height:calc(var(--spacing)*16);border-radius:var(--radius-md);border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-input);width:100%;padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*2);font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height));--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);transition-property:color,box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-outline-style:none;background-color:#0000;outline-style:none;display:flex}:is(.form textarea,.textarea)::placeholder{color:var(--color-muted-foreground)}:is(.form textarea,.textarea):focus-visible{border-color:var(--color-ring);--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-ring)}@supports (color:color-mix(in lab, red, red)){:is(.form textarea,.textarea):focus-visible{--tw-ring-color:color-mix(in oklab,var(--color-ring)50%,transparent)}}:is(.form textarea,.textarea):disabled{cursor:not-allowed;opacity:.5}:is(.form textarea,.textarea)[aria-invalid=true]{border-color:var(--color-destructive);--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.form textarea,.textarea)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)20%,transparent)}}@media (min-width:48rem){:is(.form textarea,.textarea){font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}}:is(.form textarea,.textarea):is(.dark *){background-color:var(--color-input)}@supports (color:color-mix(in lab, red, red)){:is(.form textarea,.textarea):is(.dark *){background-color:color-mix(in oklab,var(--color-input)30%,transparent)}}:is(.form textarea,.textarea):is(.dark *)[aria-invalid=true]{--tw-ring-color:var(--color-destructive)}@supports (color:color-mix(in lab, red, red)){:is(.form textarea,.textarea):is(.dark *)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--color-destructive)40%,transparent)}}.toaster{pointer-events:none;bottom:calc(var(--spacing)*0);z-index:50;width:100%;padding:calc(var(--spacing)*4);flex-direction:column-reverse;display:flex;position:fixed}@media (min-width:40rem){.toaster{max-width:calc(var(--spacing)*90)}}.toaster:not([data-align]),.toaster[data-align=end]{right:calc(var(--spacing)*0)}.toaster[data-align=start]{left:calc(var(--spacing)*0)}.toaster[data-align=center]{--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y);left:50%}.toaster .toast{pointer-events:auto;margin-top:calc(var(--spacing)*4);width:100%;transition-property:grid-template-rows,opacity,margin;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-duration:.3s;--tw-ease:var(--ease-in-out);transition-duration:.3s;transition-timing-function:var(--ease-in-out);grid-template-rows:1fr;animation:.3s ease-in-out toast-up;display:grid}.toaster .toast .toast-content{align-items:center;gap:calc(var(--spacing)*2.5);border-radius:var(--radius-lg);border-style:var(--tw-border-style);background-color:var(--color-popover);padding:calc(var(--spacing)*3);color:var(--color-popover-foreground);--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);border-width:1px;font-size:13px;display:flex;overflow:hidden}.toaster .toast .toast-content svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);flex-shrink:0}.toaster .toast .toast-content section h2{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);--tw-tracking:var(--tracking-tight);letter-spacing:var(--tracking-tight)}.toaster .toast .toast-content section p{color:var(--color-muted-foreground)}.toaster .toast .toast-content footer{gap:calc(var(--spacing)*2);flex-direction:column;margin-left:auto;display:flex}.toaster .toast .toast-content footer [data-toast-action],.toaster .toast .toast-content footer [data-toast-cancel]{height:calc(var(--spacing)*6);padding-inline:calc(var(--spacing)*2.5);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.toaster .toast[aria-hidden=true]{margin:calc(var(--spacing)*0);border-style:var(--tw-border-style);padding:calc(var(--spacing)*0);opacity:0;border-width:0;grid-template-rows:0fr;overflow:hidden}.toaster .toast[aria-hidden=true] .toast-content{border-style:var(--tw-border-style);border-width:0}[data-tooltip]{position:relative}[data-tooltip]:before{pointer-events:none;visibility:hidden;z-index:50;width:fit-content;max-width:var(--container-xs);--tw-scale-x:95%;--tw-scale-y:95%;--tw-scale-z:95%;scale:var(--tw-scale-x)var(--tw-scale-y);text-overflow:ellipsis;white-space:nowrap;border-radius:var(--radius-md);background-color:var(--color-primary);padding-inline:calc(var(--spacing)*3);padding-block:calc(var(--spacing)*1.5);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));color:var(--color-primary-foreground);opacity:0;transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-content:attr(data-tooltip);content:var(--tw-content);position:absolute;overflow:hidden}[data-tooltip]:hover:before{visibility:visible;--tw-scale-x:100%;--tw-scale-y:100%;--tw-scale-z:100%;scale:var(--tw-scale-x)var(--tw-scale-y);opacity:1}[data-tooltip]:focus-visible:not(:hover):before{display:none}:is([data-tooltip]:not([data-side]),[data-tooltip][data-side=top]):before{content:var(--tw-content);content:var(--tw-content);margin-bottom:calc(var(--spacing)*1.5);content:var(--tw-content);--tw-translate-y:calc(var(--spacing)*2);translate:var(--tw-translate-x)var(--tw-translate-y);bottom:100%}@media (hover:hover){:is([data-tooltip]:not([data-side]),[data-tooltip][data-side=top]):hover:before{content:var(--tw-content);--tw-translate-y:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}}[data-tooltip][data-side=bottom]:before{content:var(--tw-content);content:var(--tw-content);margin-top:calc(var(--spacing)*1.5);content:var(--tw-content);--tw-translate-y:calc(var(--spacing)*-2);translate:var(--tw-translate-x)var(--tw-translate-y);top:100%}@media (hover:hover){[data-tooltip][data-side=bottom]:hover:before{content:var(--tw-content);--tw-translate-y:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}}:is([data-tooltip]:not([data-side]),[data-tooltip][data-side=top],[data-tooltip][data-side=bottom])[data-align=start]:before{content:var(--tw-content);left:calc(var(--spacing)*0)}:is([data-tooltip]:not([data-side]),[data-tooltip][data-side=top],[data-tooltip][data-side=bottom])[data-align=end]:before{content:var(--tw-content);right:calc(var(--spacing)*0)}:is(:is([data-tooltip]:not([data-side]),[data-tooltip][data-side=top],[data-tooltip][data-side=bottom]):not([data-align]),:is([data-tooltip]:not([data-side]),[data-tooltip][data-side=top],[data-tooltip][data-side=bottom])[data-align=center]):before{content:var(--tw-content);content:var(--tw-content);--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y);left:50%}[data-tooltip][data-side=left]:before{content:var(--tw-content);content:var(--tw-content);margin-right:calc(var(--spacing)*1.5);content:var(--tw-content);--tw-translate-x:calc(var(--spacing)*2);translate:var(--tw-translate-x)var(--tw-translate-y);right:100%}@media (hover:hover){[data-tooltip][data-side=left]:hover:before{content:var(--tw-content);--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}}[data-tooltip][data-side=right]:before{content:var(--tw-content);content:var(--tw-content);margin-left:calc(var(--spacing)*1.5);content:var(--tw-content);--tw-translate-x:calc(var(--spacing)*-2);translate:var(--tw-translate-x)var(--tw-translate-y);left:100%}@media (hover:hover){[data-tooltip][data-side=right]:hover:before{content:var(--tw-content);--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}}:is([data-tooltip][data-side=left],[data-tooltip][data-side=right])[data-align=start]:before{content:var(--tw-content);top:calc(var(--spacing)*0)}:is([data-tooltip][data-side=left],[data-tooltip][data-side=right])[data-align=end]:before{content:var(--tw-content);bottom:calc(var(--spacing)*0)}:is(:is([data-tooltip][data-side=left],[data-tooltip][data-side=right]):not([data-align]),:is([data-tooltip][data-side=left],[data-tooltip][data-side=right])[data-align=center]):before{content:var(--tw-content);content:var(--tw-content);--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y);top:50%}}@layer utilities;:root{--radius:.625rem;--background:oklch(1 0 0);--foreground:oklch(.145 0 0);--card:oklch(1 0 0);--card-foreground:oklch(.145 0 0);--popover:oklch(1 0 0);--popover-foreground:oklch(.145 0 0);--primary:oklch(.205 0 0);--primary-foreground:oklch(.985 0 0);--secondary:oklch(.97 0 0);--secondary-foreground:oklch(.205 0 0);--muted:oklch(.97 0 0);--muted-foreground:oklch(.556 0 0);--accent:oklch(.97 0 0);--accent-foreground:oklch(.205 0 0);--destructive:oklch(.577 .245 27.325);--border:oklch(.922 0 0);--input:oklch(.922 0 0);--ring:oklch(.708 0 0);--chart-1:oklch(.646 .222 41.116);--chart-2:oklch(.6 .118 184.704);--chart-3:oklch(.398 .07 227.392);--chart-4:oklch(.828 .189 84.429);--chart-5:oklch(.769 .188 70.08);--sidebar:oklch(.985 0 0);--sidebar-foreground:oklch(.145 0 0);--sidebar-primary:oklch(.205 0 0);--sidebar-primary-foreground:oklch(.985 0 0);--sidebar-accent:oklch(.97 0 0);--sidebar-accent-foreground:oklch(.205 0 0);--sidebar-border:oklch(.922 0 0);--sidebar-ring:oklch(.708 0 0);--sidebar-width:16rem;--sidebar-mobile-width:18rem;--scrollbar-track:transparent;--scrollbar-thumb:#0000004d;--scrollbar-width:6px;--scrollbar-radius:6px;--chevron-down-icon:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"oklch(0.556 0 0)\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-chevron-down-icon lucide-chevron-down\"><path d=\"m6 9 6 6 6-6\"/></svg>");--chevron-down-icon-50:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"oklch(0.556 0 0 / 0.5)\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-chevron-down-icon lucide-chevron-down\"><path d=\"m6 9 6 6 6-6\"/></svg>");--check-icon:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"oklch(0.556 0 0)\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-check-icon lucide-check\"><path d=\"M20 6 9 17l-5-5\"/></svg>")}.dark{--background:oklch(.145 0 0);--foreground:oklch(.985 0 0);--card:oklch(.205 0 0);--card-foreground:oklch(.985 0 0);--popover:oklch(.269 0 0);--popover-foreground:oklch(.985 0 0);--primary:oklch(.922 0 0);--primary-foreground:oklch(.205 0 0);--secondary:oklch(.269 0 0);--secondary-foreground:oklch(.985 0 0);--muted:oklch(.269 0 0);--muted-foreground:oklch(.708 0 0);--accent:oklch(.371 0 0);--accent-foreground:oklch(.985 0 0);--destructive:oklch(.704 .191 22.216);--border:oklch(1 0 0/10%);--input:oklch(1 0 0/15%);--ring:oklch(.556 0 0);--chart-1:oklch(.488 .243 264.376);--chart-2:oklch(.696 .17 162.48);--chart-3:oklch(.769 .188 70.08);--chart-4:oklch(.627 .265 303.9);--chart-5:oklch(.645 .246 16.439);--sidebar:oklch(.205 0 0);--sidebar-foreground:oklch(.985 0 0);--sidebar-primary:oklch(.488 .243 264.376);--sidebar-primary-foreground:oklch(.985 0 0);--sidebar-accent:oklch(.269 0 0);--sidebar-accent-foreground:oklch(.985 0 0);--sidebar-border:oklch(1 0 0/10%);--sidebar-ring:oklch(.439 0 0);--scrollbar-thumb:#ffffff4d;--chevron-down-icon:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"oklch(0.708 0 0)\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-chevron-down-icon lucide-chevron-down\"><path d=\"m6 9 6 6 6-6\"/></svg>");--chevron-down-icon-50:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"oklch(0.708 0 0 / 0.5)\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-chevron-down-icon lucide-chevron-down\"><path d=\"m6 9 6 6 6-6\"/></svg>");--check-icon:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"oklch(0.708 0 0)\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-check-icon lucide-check\"><path d=\"M20 6 9 17l-5-5\"/></svg>");color-scheme:dark}@media not all and (min-width:48rem){.sidebar:not([data-sidebar-initialized]){display:none}:is(.sidebar:not([aria-hidden]),.sidebar[aria-hidden=false]){inset:calc(var(--spacing)*0);z-index:40;background-color:#00000080;position:fixed}@supports (color:color-mix(in lab, red, red)){:is(.sidebar:not([aria-hidden]),.sidebar[aria-hidden=false]){background-color:color-mix(in oklab,var(--color-black)50%,transparent)}}}.sidebar nav{inset-block:calc(var(--spacing)*0);z-index:50;width:var(--sidebar-mobile-width);background-color:var(--color-sidebar);color:var(--color-sidebar-foreground);transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-duration:.3s;--tw-ease:var(--ease-in-out);transition-duration:.3s;transition-timing-function:var(--ease-in-out);flex-direction:column;display:flex;position:fixed}@media (min-width:48rem){.sidebar nav{width:var(--sidebar-width)}}.sidebar+*{transition-property:margin;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-duration:.3s;--tw-ease:var(--ease-in-out);transition-duration:.3s;transition-timing-function:var(--ease-in-out)}:is(.sidebar:not([data-side]),.sidebar[data-side=left]) nav{left:calc(var(--spacing)*0);border-right-style:var(--tw-border-style);border-right-width:1px}:is(.sidebar:not([data-side]),.sidebar[data-side=left])+*{position:relative}@media (min-width:48rem){:is(.sidebar:not([data-side]),.sidebar[data-side=left])+*{margin-left:var(--sidebar-width)}}:is(.sidebar:not([data-side]),.sidebar[data-side=left])[aria-hidden=true] nav{--tw-translate-x:-100%;translate:var(--tw-translate-x)var(--tw-translate-y)}@media (min-width:48rem){:is(.sidebar:not([data-side]),.sidebar[data-side=left])[aria-hidden=true]+*{margin-left:calc(var(--spacing)*0)}}.sidebar[data-side=right] nav{right:calc(var(--spacing)*0);border-left-style:var(--tw-border-style);border-left-width:1px}.sidebar[data-side=right]+*{position:relative}@media (min-width:48rem){.sidebar[data-side=right]+*{margin-right:var(--sidebar-width)}}.sidebar[data-side=right][aria-hidden=true] nav{--tw-translate-x:100%;translate:var(--tw-translate-x)var(--tw-translate-y)}@media (min-width:48rem){.sidebar[data-side=right][aria-hidden=true]+*{margin-right:calc(var(--spacing)*0)}}.sidebar nav>header,.sidebar nav>footer{gap:calc(var(--spacing)*2);padding:calc(var(--spacing)*2);flex-direction:column;display:flex}.sidebar nav [role=separator]{margin-inline:calc(var(--spacing)*2);border-color:var(--color-sidebar-border);width:auto}.sidebar nav>section{min-height:calc(var(--spacing)*0);gap:calc(var(--spacing)*2);flex-direction:column;flex:1;display:flex;overflow-y:auto}.sidebar nav>section>[role=group]{width:100%;min-width:calc(var(--spacing)*0);padding:calc(var(--spacing)*2);flex-direction:column;display:flex;position:relative}.sidebar nav>section h3{height:calc(var(--spacing)*8);border-radius:var(--radius-md);padding-inline:calc(var(--spacing)*2);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:var(--color-sidebar-foreground);--tw-ring-color:var(--color-sidebar-ring);--tw-outline-style:none;transition-property:margin,opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-duration:.2s;--tw-ease:linear;outline-style:none;flex-shrink:0;align-items:center;transition-duration:.2s;transition-timing-function:linear;display:flex}@supports (color:color-mix(in lab, red, red)){.sidebar nav>section h3{color:color-mix(in oklab,var(--color-sidebar-foreground)70%,transparent)}}@media (forced-colors:active){.sidebar nav>section h3{outline-offset:2px;outline:2px solid #0000}}.sidebar nav>section h3:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.sidebar nav>section h3>svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);flex-shrink:0}.sidebar nav>section ul{width:100%;min-width:calc(var(--spacing)*0);gap:calc(var(--spacing)*1);flex-direction:column;display:flex}.sidebar nav>section ul li{position:relative}.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary{align-items:center;gap:calc(var(--spacing)*2);border-radius:var(--radius-md);width:100%;padding:calc(var(--spacing)*2);text-align:left;font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height));--tw-ring-color:var(--color-sidebar-ring);--tw-outline-style:none;transition-property:width,height,padding;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));outline-style:none;display:flex;overflow:hidden}@media (forced-colors:active){:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary){outline-offset:2px;outline:2px solid #0000}}@media (hover:hover){:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary):hover{background-color:var(--color-sidebar-accent);color:var(--color-sidebar-accent-foreground)}}:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary):focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary):active{background-color:var(--color-sidebar-accent);color:var(--color-sidebar-accent-foreground)}:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary):disabled,:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary)[aria-disabled=true]{pointer-events:none;opacity:.5}:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary)>span:last-child{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary)>svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4);flex-shrink:0}:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary)[aria-current=page]{background-color:var(--color-sidebar-accent);--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium);color:var(--color-sidebar-accent-foreground)}@media (hover:hover){:is(:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary):not([data-variant]),:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary)[data-variant=default]):hover{background-color:var(--color-sidebar-accent);color:var(--color-sidebar-accent-foreground)}}:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary)[data-variant=outline]{background-color:var(--color-background);--tw-shadow:0 0 0 1px var(--tw-shadow-color,hsl(var(--sidebar-border)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary)[data-variant=outline]:hover{background-color:var(--color-sidebar-accent);color:var(--color-sidebar-accent-foreground);--tw-shadow:0 0 0 1px var(--tw-shadow-color,hsl(var(--sidebar-accent)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary):not([data-size]),:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary)[data-size=default]{height:calc(var(--spacing)*8);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary)[data-size=sm]{height:calc(var(--spacing)*7);font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary)[data-size=lg]{height:calc(var(--spacing)*12);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}:is(.sidebar nav>section ul li>a,.sidebar nav>section ul li>details>summary)[data-size=lg]:is(:where(.group)[data-collapsible=icon] *){padding:calc(var(--spacing)*0)!important}.sidebar nav>section ul li>details:not([open])>summary:after{rotate:-90deg}.sidebar nav>section ul li>details>summary:after{width:calc(var(--spacing)*3.5);height:calc(var(--spacing)*3.5);background-color:var(--color-primary);transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration));--tw-ease:linear;--tw-content:"";content:var(--tw-content);-webkit-mask-image:var(--chevron-down-icon);-webkit-mask-image:var(--chevron-down-icon);mask-image:var(--chevron-down-icon);margin-left:auto;transition-timing-function:linear;display:block;-webkit-mask-position:50%;mask-position:50%;-webkit-mask-size:1rem;mask-size:1rem;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}.sidebar nav>section ul li>details::details-content{padding-inline:calc(var(--spacing)*3.5)}.sidebar nav>section ul ul{width:100%;min-width:calc(var(--spacing)*0);--tw-translate-x:1px;translate:var(--tw-translate-x)var(--tw-translate-y);gap:calc(var(--spacing)*1);border-left-style:var(--tw-border-style);border-left-width:1px;border-color:var(--color-sidebar-border);padding-inline:calc(var(--spacing)*2.5);padding-block:calc(var(--spacing)*.5);flex-direction:column;display:flex}@keyframes toast-up{0%{opacity:0;transform:translateY(100%)}}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-tracking{syntax:"*";inherits:false}@property --tw-leading{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-content{syntax:"*";inherits:false;initial-value:""}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}