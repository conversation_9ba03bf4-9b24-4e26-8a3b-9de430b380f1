@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --sidebar-width: 16rem;
  --sidebar-mobile-width: 18rem;
  --scrollbar-track: transparent;
  --scrollbar-thumb: rgba(0, 0, 0, 0.3);
  --scrollbar-width: 6px;
  --scrollbar-radius: 6px;
  --chevron-down-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="oklch(0.556 0 0)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down-icon lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>'); /* --muted-foreground */
  --chevron-down-icon-50: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="oklch(0.556 0 0 / 0.5)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down-icon lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>'); /* --muted-foreground + 50% opacity */
  --check-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="oklch(0.556 0 0)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>'); /* --muted-foreground */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.269 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.371 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.439 0 0);
  --scrollbar-thumb: rgba(255, 255, 255, 0.3);
  --chevron-down-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="oklch(0.708 0 0)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down-icon lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>'); /* --muted-foreground */
  --chevron-down-icon-50: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="oklch(0.708 0 0 / 0.5)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down-icon lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>'); /* --muted-foreground + 50% opacity */
  --check-icon: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="oklch(0.708 0 0)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-icon lucide-check"><path d="M20 6 9 17l-5-5"/></svg>');/* --muted-foreground */
  color-scheme: dark;
}

@theme {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  html {
    @apply scroll-smooth;
  }
  body {
    @apply bg-background text-foreground overscroll-none antialiased;
  }
  .scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);

    &::-webkit-scrollbar {
      width: var(--scrollbar-width);
    }
    &::-webkit-scrollbar-track {
      background: var(--scrollbar-track);
    }
    &::-webkit-scrollbar-thumb {
      background: var(--scrollbar-thumb);
      border-radius: var(--scrollbar-radius);
    }
  }
  /* Alpine.js */
  [x-cloak] {
    display: none !important;
  }
}

/* Alert */
@layer components {
  .alert,
  .alert-destructive {
    @apply relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current;
    
    h2 {
      @apply col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight;
    }
    section {
      @apply text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed;

      ul {
        @apply list-inside list-disc text-sm;
      }
    }
  }
  .alert {
    @apply bg-card text-card-foreground;
  }
  .alert-destructive {
    @apply text-destructive bg-card [&>svg]:text-current;

    section {
      @apply text-destructive;
    }
  }
}

/* Button */
@layer components {
  .btn,
  .btn-primary,
  .btn-secondary,
  .btn-outline,
  .btn-ghost,
  .btn-link,
  .btn-destructive,
  .btn-sm,
  .btn-sm-primary,
  .btn-sm-secondary,
  .btn-sm-outline,
  .btn-sm-ghost,
  .btn-sm-link,
  .btn-sm-destructive,
  .btn-lg,
  .btn-lg-primary,
  .btn-lg-secondary,
  .btn-lg-outline,
  .btn-lg-ghost,
  .btn-lg-link,
  .btn-lg-destructive,
  .btn-icon,
  .btn-icon-primary,
  .btn-icon-secondary,
  .btn-icon-outline,
  .btn-icon-ghost,
  .btn-icon-link,
  .btn-icon-destructive,
  .btn-sm-icon,
  .btn-sm-icon-primary,
  .btn-sm-icon-secondary,
  .btn-sm-icon-outline,
  .btn-sm-icon-ghost,
  .btn-sm-icon-link,
  .btn-sm-icon-destructive,
  .btn-lg-icon,
  .btn-lg-icon-primary,
  .btn-lg-icon-secondary,
  .btn-lg-icon-outline,
  .btn-lg-icon-ghost,
  .btn-lg-icon-link,
  .btn-lg-icon-destructive {
    @apply inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer rounded-md;
  }
  .btn,
  .btn-primary,
  .btn-secondary,
  .btn-outline,
  .btn-ghost,
  .btn-link,
  .btn-destructive {
    @apply gap-2 h-9 px-4 py-2 has-[>svg]:px-3;
  }
  .btn-icon,
  .btn-icon-primary,
  .btn-icon-secondary,
  .btn-icon-outline,
  .btn-icon-ghost,
  .btn-icon-link,
  .btn-icon-destructive {
    @apply size-9;
  }
  .btn-sm,
  .btn-sm-primary,
  .btn-sm-secondary,
  .btn-sm-outline,
  .btn-sm-ghost,
  .btn-sm-link,
  .btn-sm-destructive {
    @apply gap-1.5 h-8 px-3 has-[>svg]:px-2.5;
  }
  .btn-sm-icon,
  .btn-sm-icon-primary,
  .btn-sm-icon-secondary,
  .btn-sm-icon-outline,
  .btn-sm-icon-ghost,
  .btn-sm-icon-link,
  .btn-sm-icon-destructive {
    @apply size-8;
  }
  .btn-lg,
  .btn-lg-primary,
  .btn-lg-secondary,
  .btn-lg-outline,
  .btn-lg-ghost,
  .btn-lg-link,
  .btn-lg-destructive {
    @apply gap-2 h-10 px-6 has-[>svg]:px-4;
  }
  .btn-lg-icon,
  .btn-lg-icon-primary,
  .btn-lg-icon-secondary,
  .btn-lg-icon-outline,
  .btn-lg-icon-ghost,
  .btn-lg-icon-link,
  .btn-lg-icon-destructive {
    @apply size-10;
  }
  .btn,
  .btn-primary,
  .btn-sm,
  .btn-sm-primary,
  .btn-lg,
  .btn-lg-primary,
  .btn-icon,
  .btn-icon-primary,
  .btn-sm-icon,
  .btn-sm-icon-primary,
  .btn-lg-icon,
  .btn-lg-icon-primary {  
    @apply bg-primary text-primary-foreground shadow-xs hover:bg-primary/90;
    &[aria-pressed='true'] {
      @apply bg-primary/90;
    }
  }
  .btn-secondary,
  .btn-sm-secondary,
  .btn-lg-secondary,
  .btn-icon-secondary,
  .btn-sm-icon-secondary,
  .btn-lg-icon-secondary {
    @apply bg-secondary text-secondary-foreground shadow-xs;
    &:hover,
    &[aria-pressed='true'] {
      @apply bg-secondary/80;
    }
  }
  .btn-outline,
  .btn-sm-outline,
  .btn-lg-outline,
  .btn-icon-outline,
  .btn-sm-icon-outline,
  .btn-lg-icon-outline {
    @apply border bg-background shadow-xs dark:bg-input/30 dark:border-input;
    &:hover,
    &[aria-pressed='true'] {
      @apply bg-accent text-accent-foreground dark:bg-accent/50;
    }
  }
  .btn-ghost,
  .btn-sm-ghost,
  .btn-lg-ghost,
  .btn-icon-ghost,
  .btn-sm-icon-ghost,
  .btn-lg-icon-ghost {
    &:hover,
    &[aria-pressed='true'] {
      @apply bg-accent text-accent-foreground dark:bg-accent/50;
    }
  }
  .btn-link,
  .btn-sm-link,
  .btn-lg-link,
  .btn-icon-link,
  .btn-sm-icon-link,
  .btn-lg-icon-link {
    @apply text-primary underline-offset-4;
    &:hover,
    &[aria-pressed='true'] {
      @apply hover:underline;
    }
  }
  .btn-destructive,
  .btn-sm-destructive,
  .btn-lg-destructive,
  .btn-icon-destructive,
  .btn-sm-icon-destructive,
  .btn-lg-icon-destructive {
    @apply bg-destructive text-white shadow-xs focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60;
    &:hover,
    &[aria-pressed='true'] {
      @apply bg-destructive/90 dark:bg-destructive/50;
    }
  }
}

/* Badge */
@layer components {
  .badge,
  .badge-primary,
  .badge-secondary,
  .badge-destructive,
  .badge-outline {
    @apply inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden;
  }
  .badge,
  .badge-primary {
    @apply border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90;
  }
  .badge-secondary {
    @apply border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90;
  }
  .badge-destructive {
    @apply border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60;
  }
  .badge-outline {
    @apply text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground;
  }
}

/* Card */
@layer components {
  .card {
    @apply bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm;

    > header {
      @apply @container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6;
      /* TODO: CLEAN has-data-[slot=card-action] */

      h2 {
        @apply leading-none font-semibold;
      }
      p {
        @apply text-muted-foreground text-sm;
      }
    }
    > section {
      @apply px-6;
    }
    > footer {
      @apply flex items-center px-6 [.border-t]:pt-6;
    }
  }
}

/* Checkbox */
@layer components {
  .form input[type='checkbox']:not([role='switch']),
  .input[type='checkbox']:not([role='switch']) {
    @apply appearance-none border-input dark:bg-input/30 checked:bg-primary dark:checked:bg-primary checked:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50;
    
    &:checked:after {
      @apply content-[''] block size-3.5 bg-primary-foreground;
      @apply mask-[image:var(--check-icon)] mask-size-[0.875rem] mask-no-repeat mask-center;
    }
  }
}

/* Collapsible */
@layer components {
  details {
    &::details-content {
      block-size: 0;
      @apply block opacity-0 transition-discrete transition-all;
    }
    &[open]::details-content {
      block-size: auto;
      block-size: calc-size(auto, size);
      @apply opacity-100;
    }
    summary {
      @apply inline-flex items-center cursor-pointer;
    }
  }
  details > summary::-webkit-details-marker {
    display: none;
  }
}

/* Dialog */
@layer components {
  .dialog {
    @apply inset-y-0 opacity-0 transition-all transition-discrete;

    &:is([open],:popover-open) {
      @apply opacity-100;

      &::backdrop {
        @apply opacity-100;
      }
      > article {
        @apply scale-100;
      }

      @starting-style {
        @apply opacity-0;

        &::backdrop {
          @apply opacity-0;
        }
        > article {
          @apply scale-95;
        }
      }
    }
    &::backdrop {
      @apply bg-black/50 opacity-0 transition-all transition-discrete;
    }
    > article {
      @apply bg-background fixed top-[50%] left-[50%] z-50 flex flex-col w-full max-w-[calc(100%_-_2rem)] -translate-x-1/2 -translate-y-1/2 gap-4 rounded-lg border p-6 shadow-lg sm:max-w-lg max-h-[calc(100%_-_2rem)];
      @apply transition-all scale-95;

      > header {
        @apply flex flex-col gap-2 text-center sm:text-left;

        > h2 {
          @apply text-lg leading-none font-semibold;
        }
        > p {
          @apply text-muted-foreground text-sm;
        }
      }
      > section {
        @apply flex-1 -mx-6 px-6;
      }
      > footer {
        @apply flex flex-col-reverse gap-2 sm:flex-row sm:justify-end;
      }
      > form[method='dialog'] {
        @apply absolute top-4 right-4;
        > button {
          @apply ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground  rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4;
        }
      }
    }
  }
}

/* Dropdown Menu */
@layer components {
  .dropdown-menu {
    @apply relative inline-flex;

    [data-popover] {
      @apply p-1;
      min-width: anchor-size(width);

      [role='menuitem'],
      [role='menuitemcheckbox'],
      [role='menuitemradio'] {
        @apply aria-hidden:hidden [&_svg]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none [&_svg]:shrink-0 [&_svg]:size-4 aria-disabled:opacity-50 aria-disabled:pointer-events-none disabled:opacity-50 disabled:pointer-events-none w-full truncate;

        &:not([aria-disabled='true']) {
          @apply focus-visible:bg-accent focus-visible:text-accent-foreground;
        }

        &.active {
          @apply bg-accent text-accent-foreground;
        }
      }
      [role='menu'] [role='heading'] {
        @apply flex px-2 py-1.5 text-sm font-medium;
      }
      [role='separator'] {
        @apply border-border -mx-1 my-1;
      }
    }
    &:not([data-dropdown-menu-initialized]) [data-popover] {
      [role='menuitem'],
      [role='menuitemcheckbox'],
      [role='menuitemradio'] {
        @apply hover:bg-accent hover:text-accent-foreground;
      }
    }
  }
}

/* Input */
@layer components {
  .form input[type='text'],
  .form input[type='email'],
  .form input[type='password'],
  .form input[type='number'],
  .form input[type='file'],
  .form input[type='tel'],
  .form input[type='url'],
  .form input[type='search'],
  .form input[type='date'],
  .form input[type='datetime-local'],
  .form input[type='month'],
  .form input[type='week'],
  .form input[type='time'],
  .input[type='text'],
  .input[type='email'],
  .input[type='password'],
  .input[type='number'],
  .input[type='file'],
  .input[type='tel'],
  .input[type='url'],
  .input[type='search'],
  .input[type='date'],
  .input[type='datetime-local'],
  .input[type='month'],
  .input[type='week'],
  .input[type='time'] {
    @apply appearance-none file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm;
    @apply focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px];
    @apply aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive;
  }
}

/* Label */
@layer components {
  .form label,
  .label {
    @apply flex items-center gap-2 text-sm leading-none font-medium select-none peer-disabled:pointer-events-none peer-disabled:opacity-50;

     &:has(>*:disabled),
     &:has(+*:disabled) {
       @apply opacity-50 pointer-events-none;
     }
  }
}

/* Popover */
@layer components {
  [data-popover] {
    @apply absolute bg-popover text-popover-foreground overflow-x-hidden overflow-y-auto rounded-md border shadow-md z-50 visible opacity-100 scale-100 min-w-full w-max transition-all;

    &[aria-hidden='true'] {
      @apply invisible opacity-0 scale-95;
      
      &:not([data-side]),
      &[data-side='bottom'] {
        @apply -translate-y-2;
      }
      &[data-side='top'] {
        @apply translate-y-2;
      }
      &[data-side='left'] {
        @apply translate-x-2;
      }
      &[data-side='right'] {
        @apply -translate-x-2;
      }
    }
    &:not([data-side]),
    &[data-side='bottom'] {
      @apply mt-1 top-full;
    }
    &[data-side='top'] {
      @apply mb-1 bottom-full;
    }
    &[data-side='left'] {
      @apply mr-1 right-full;
    }
    &[data-side='right'] {
      @apply ml-1 left-full;
    }
    &:not([data-side]),
    &[data-side='bottom'],
    &[data-side='top'] {
      &:not([data-align]),
      &[data-align='start'] {
        @apply left-0;
      }
      &[data-align='end'] {
        @apply right-0;
      }
      &[data-align='center'] {
        @apply left-1/2 -translate-x-1/2;
      }
    }
    &[data-side='left'],
    &[data-side='right'] {
      &:not([data-align]),
      &[data-align='start'] {
        @apply top-0;
      }
      &[data-align='end'] {
        @apply bottom-0;
      }
      &[data-align='center'] {
        @apply top-1/2 -translate-y-1/2;
      }
    }
  }
  .popover {
    @apply relative inline-flex;

    [data-popover] {
      @apply p-4;
    }
  } 
}

/* Radio */
@layer components {
  .form input[type='radio'],
  .input[type='radio'] {
    @apply appearance-none border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 relative;

    &:checked:before {
      @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 content-[''] rounded-full size-2 bg-primary;
    }
  }
}

/* Range */
@layer components {
  .form input[type='range'],
  .input[type='range'] {
    @apply appearance-none flex items-center p-0 outline-none;
    --slider-value: 20%;

    &:hover,
    &:focus-visible {
      &::-webkit-slider-thumb {
        @apply ring-4;
      }
      &::-moz-range-thumb {
        @apply ring-4;
      }
      &::-ms-thumb {
        @apply ring-4;
      }
    }

    &::-webkit-slider-thumb {
      @apply appearance-none border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm -mt-1.25;
    }
    &::-webkit-slider-runnable-track {
      @apply appearance-none rounded-full h-1.5 w-full;
      background: linear-gradient(to right, var(--primary) var(--slider-value), var(--muted) var(--slider-value));
    }    
    &::-moz-range-thumb {
      @apply appearance-none border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm -mt-1.25;
    }
    &::-moz-range-track {
      @apply appearance-none rounded-full h-1.5 w-full;
      background: linear-gradient(to right, var(--primary) var(--slider-value), var(--muted) var(--slider-value));
    }
    &::-ms-thumb {
      @apply appearance-none border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm -mt-1.25;
    }
    &::-ms-track {
      @apply appearance-none rounded-full h-1.5 w-full;
    }
    &::-ms-fill-lower {
      @apply bg-primary rounded-full;
    }
    &::-ms-fill-upper {
      @apply bg-muted rounded-full;
    }
  }
}

/* Select */
@layer components {
  .form select,
  select.select {
    @apply appearance-none border-input focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent pl-3 pr-9 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 h-9;
    @apply bg-[image:var(--chevron-down-icon-50)] bg-no-repeat bg-position-[center_right_0.75rem] bg-size-[1rem];

    option,
    optgroup {
      @apply bg-popover text-popover-foreground;
    }
  }
  *:not(select).select {
    @apply relative inline-flex;

    [data-popover] {
      @apply p-1;

      [role='option'] {
        @apply aria-hidden:hidden [&_svg]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm pl-2 py-1.5 pr-7.5 text-sm outline-hidden select-none [&_svg]:shrink-0 [&_svg]:size-4 aria-disabled:opacity-50 aria-disabled:pointer-events-none disabled:opacity-50 disabled:pointer-events-none w-full truncate;
        
        &[aria-selected='true'] {
          @apply bg-[image:var(--check-icon)] bg-no-repeat bg-position-[center_right_0.5rem] bg-size-[0.875rem];
        }
        &.active,
        &:focus-visible {
          @apply bg-accent text-accent-foreground;
        }
      }
      [role='listbox'] [role='heading'] {
        @apply flex text-muted-foreground px-2 py-1.5 text-xs;
      }
      [role='listbox'] [role='group']:not(:has([role='option']:not([aria-hidden='true']))) {
        @apply hidden;
      }
      [role='separator'] {
        @apply border-border -mx-1 my-1;
      }
      > header {
        @apply flex h-9 items-center gap-2 border-b px-3 -mx-1 -mt-1 mb-1;

        svg {
          @apply size-4 shrink-0 opacity-50;
        }
        input[role='combobox'] {
          @apply placeholder:text-muted-foreground flex h-10 flex-1 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50 min-w-0;
        }
      }
      [role='listbox']:not(:has([data-value]:not([aria-hidden='true'])))::before {
        @apply flex items-center justify-center p-6 text-sm truncate;
      }
      [role='listbox'][data-empty]:not(:has([data-value]:not([aria-hidden='true'])))::before {
        @apply content-[attr(data-empty)];
      }
      [role='listbox']:not([data-empty]):not(:has([data-value]:not([aria-hidden='true'])))::before {
        @apply content-['No_results_found'];
      }
    }
    &:not([data-select-initialized]) [data-popover] [role='option'] {
      @apply hover:bg-accent hover:text-accent-foreground;
    }
  }
}

/* Sidebar */
.sidebar {
  &:not([data-sidebar-initialized]) {
    @apply max-md:hidden;
  }
  &:not([aria-hidden]),
  &[aria-hidden=false] {
    @apply max-md:bg-black/50 max-md:fixed max-md:inset-0 max-md:z-40;
  }
  nav {
    @apply bg-sidebar text-sidebar-foreground flex flex-col w-(--sidebar-mobile-width) md:w-(--sidebar-width) fixed inset-y-0 z-50 transition-transform ease-in-out duration-300;
  }
  & + * {
    @apply transition-[margin] ease-in-out duration-300;
  }
  &:not([data-side]),
  &[data-side=left] {
    nav {
      @apply left-0 border-r;
    }
    & + * {
      @apply relative md:ml-(--sidebar-width);
    }
    &[aria-hidden=true] {
      nav {
        @apply -translate-x-full;
      }
      & + * {
        @apply md:ml-0;
      }
    }
  }
  &[data-side=right] {
    nav {
      @apply right-0 border-l;
    }
    & + * {
      @apply relative md:mr-(--sidebar-width);
    }
    &[aria-hidden=true] {
      nav {
        @apply translate-x-full;
      }
      & + * {
        @apply md:mr-0;
      }
    }
  }
  nav {
    > header,
    > footer {
      @apply flex flex-col gap-2 p-2;
    }
    [role=separator] {
      @apply border-sidebar-border mx-2 w-auto;
    }
    > section {
      @apply flex min-h-0 flex-1 flex-col gap-2 overflow-y-auto;

      > [role=group] {
        @apply relative flex w-full min-w-0 flex-col p-2;
      }
      h3 {
        @apply text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0;
      }
      ul {
        @apply flex w-full min-w-0 flex-col gap-1;

        li {
          @apply relative;

          > a,
          > details > summary {
            @apply flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&[aria-current=page]]:bg-sidebar-accent [&[aria-current=page]]:font-medium [&[aria-current=page]]:text-sidebar-accent-foreground [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0;

            &:not([data-variant]),
            &[data-variant=default] {
              @apply hover:bg-sidebar-accent hover:text-sidebar-accent-foreground;
            }
            &[data-variant=outline] {
              @apply bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))];
            }
            &:not([data-size]),
            &[data-size=default] {
              @apply h-8 text-sm;
            }
            &[data-size=sm] {
              @apply h-7 text-xs;
            }
            &[data-size=lg] {
              @apply h-12 text-sm group-data-[collapsible=icon]:p-0!;
            }
          }
          > details {
            &:not([open]) {
              > summary {
                &::after {
                  @apply -rotate-90;
                }
              }
            }
            > summary {
              &::after {
                @apply content-[''] block size-3.5 bg-primary ml-auto transition-transform ease-linear;
                @apply mask-[image:var(--chevron-down-icon)] mask-size-[1rem] mask-no-repeat mask-center;
              }
            }
            &::details-content {
              @apply px-3.5;
            }
          }
        }
        ul {
          @apply border-sidebar-border flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5 w-full;
        }
      }
    }
  }
}

/* Switch */
@layer components {
  .form input[type='checkbox'][role='switch'],
  .input[type='checkbox'][role='switch'] {
    @apply appearance-none focus-visible:border-ring focus-visible:ring-ring/50 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50;
    @apply bg-input dark:bg-input/80 checked:bg-primary dark:checked:bg-primary;
    @apply before:content-[''] before:pointer-events-none before:block before:size-4 before:rounded-full before:ring-0 before:transition-all;
    @apply before:bg-background dark:before:bg-foreground;
    @apply dark:checked:before:bg-primary-foreground checked:before:ms-3.5;
  }
}

/* Tables */
@layer components {
  .table {
    @apply w-full caption-bottom text-sm;
    thead {
      @apply [&_tr]:border-b;
    }
    tbody {
      @apply [&_tr:last-child]:border-0;
    }
    tfoot {
      @apply bg-muted/50 border-t font-medium [&>tr]:last:border-b-0;
    }
    tr {
      @apply hover:bg-muted/50 border-b transition-colors;
    }
    th {
      @apply text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px];
    }
    td {
      @apply p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px];
    }
    caption {
      @apply text-muted-foreground mt-4 text-sm;
    }
  }
}

/* Tabs */
@layer components {
  .tabs {
    @apply flex flex-col gap-2;
    
    [role='tablist'] {
      @apply bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px];

      [role='tab'] {
        @apply focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring text-foreground dark:text-muted-foreground inline-flex h-[calc(100%_-_1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4;

        &[aria-selected='true'] {
          @apply bg-background dark:text-foreground dark:border-input dark:bg-input/30 shadow-sm;
        }
      }
    }
    [role='tabpanel'] {
      @apply flex-1 outline-none;
    }
  }
}

/* Textarea */
@layer components {
  .form textarea,
  .textarea {
    @apply border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm;
  }
}

/* Toasts */
@layer components {
  .toaster {
    @apply fixed bottom-0 p-4 pointer-events-none z-50 w-full sm:max-w-90 flex flex-col-reverse;

    &:not([data-align]),
    &[data-align='end'] {
      @apply right-0;
    }
    &[data-align='start'] {
      @apply left-0;
    }
    &[data-align='center'] {
      @apply left-1/2 -translate-x-1/2;
    }
    .toast {
      @apply pointer-events-auto w-full mt-4 animate-[toast-up_0.3s_ease-in-out] grid  grid-rows-[1fr] transition-[grid-template-rows,opacity,margin] duration-300 ease-in-out;
      
      .toast-content {
        @apply text-popover-foreground text-[13px] bg-popover border shadow-lg rounded-lg overflow-hidden flex gap-2.5 p-3 items-center;

        svg {
          @apply size-4 shrink-0;
        }
        section {
          h2 {
            @apply font-medium tracking-tight;
          }
          p {
            @apply text-muted-foreground;
          }
        }
        footer {
          @apply ml-auto flex flex-col gap-2;

          [data-toast-action],
          [data-toast-cancel] {
            @apply h-6 px-2.5 text-xs;
          }
        }
      }
      &[aria-hidden='true'] {
        @apply grid-rows-[0fr] opacity-0 m-0 border-0 p-0 overflow-hidden;

        .toast-content {
          @apply border-0;
        }
      }
    }
  }
}
@keyframes toast-up {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
}

/* Tooltip */
@layer components {
  [data-tooltip] {
    @apply relative;

    &:before {
      @apply absolute content-[attr(data-tooltip)] bg-primary text-primary-foreground z-50 truncate max-w-xs w-fit rounded-md px-3 py-1.5 text-xs invisible opacity-0 scale-95 transition-all pointer-events-none;
    }
    &:hover:before {
      @apply visible opacity-100 scale-100;
    }
    &:focus-visible:not(:hover):before {
      @apply hidden;
    }
    &:not([data-side]),
    &[data-side='top'] {
    @apply before:bottom-full before:mb-1.5 before:translate-y-2 hover:before:translate-y-0 ;
    }
    &[data-side='bottom'] {
      @apply before:top-full before:mt-1.5 before:-translate-y-2 hover:before:translate-y-0 ;
    }
    &:not([data-side]),
    &[data-side='top'],
    &[data-side='bottom'] {
      &[data-align='start'] {
        @apply before:left-0;
      }
      &[data-align='end'] {
        @apply before:right-0;
      }
      &:not([data-align]),
      &[data-align='center'] {
        @apply before:left-1/2 before:-translate-x-1/2; 
      }
    }
    &[data-side='left'] {
      @apply before:right-full before:mr-1.5 before:translate-x-2 hover:before:translate-x-0;
    }
    &[data-side='right'] {
      @apply before:left-full before:ml-1.5 before:-translate-x-2 hover:before:translate-x-0;
    }
    &[data-side='left'],
    &[data-side='right'] {
      &[data-align='start'] {
        @apply before:top-0;
      }
      &[data-align='end'] {
        @apply before:bottom-0;
      }
      &:not([data-align]),
      &[data-align='center'] {
        @apply before:top-1/2 before:-translate-y-1/2;
      }
    }
  }
}