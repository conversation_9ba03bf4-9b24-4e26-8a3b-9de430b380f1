import{jsxs as t,jsx as n}from"react/jsx-runtime";import{createContext as e,useContext as o,useMemo as r,use<PERSON>allback as a,useLayoutEffect as i,useEffect as s,useRef as u,useInsertionEffect as l,forwardRef as c,Fragment as d,createElement as f}from"react";import{i as m,a as p,f as y,b as g,c as v,P as h,S,o as M,m as b,L as w,d as E,e as j,g as C,h as P,j as V,k as A,r as T,l as L,n as W,p as x,s as I,q as O}from"./size-rollup-dom-max-assets.js";const k=e({strict:!1}),D=e({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),R=e({});function F(t){const{initial:n,animate:e}=function(t,n){if(m(t)){const{initial:n,animate:e}=t;return{initial:!1===n||p(n)?n:void 0,animate:p(e)?e:void 0}}return!1!==t.inherit?n:{}}(t,o(R));return r(()=>({initial:n,animate:e}),[H(n),H(e)])}function H(t){return Array.isArray(t)?t.join(" "):t}const N=Symbol.for("motionComponentSymbol");function q(t,n,e){return a(o=>{o&&t.onMount&&t.onMount(o),n&&(o?n.mount(o):n.unmount()),e&&("function"==typeof e?e(o):g(e)&&(e.current=o))},[n])}const B=v?i:s;function U(t,n,e,r,a){const{visualElement:i}=o(R),c=o(k),d=o(h),f=o(D).reducedMotion,m=u(null);r=r||c.renderer,!m.current&&r&&(m.current=r(t,{visualState:n,parent:i,props:e,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:f}));const p=m.current,y=o(S);!p||p.projection||!a||"html"!==p.type&&"svg"!==p.type||function(t,n,e,o){const{layoutId:r,layout:a,drag:i,dragConstraints:s,layoutScroll:u,layoutRoot:l,layoutCrossfade:c}=n;t.projection=new e(t.latestValues,n["data-framer-portal-id"]?void 0:$(t.parent)),t.projection.setOptions({layoutId:r,layout:a,alwaysMeasureLayout:Boolean(i)||s&&g(s),visualElement:t,animationType:"string"==typeof a?a:"both",initialPromotionConfig:o,crossfade:c,layoutScroll:u,layoutRoot:l})}(m.current,e,a,y);const v=u(!1);l(()=>{p&&v.current&&p.update(e,d)});const w=e[M],E=u(Boolean(w)&&!window.MotionHandoffIsComplete?.(w)&&window.MotionHasOptimisedAnimation?.(w));return B(()=>{p&&(v.current=!0,window.MotionIsMounted=!0,p.updateFeatures(),b.render(p.render),E.current&&p.animationState&&p.animationState.animateChanges())}),s(()=>{p&&(!E.current&&p.animationState&&p.animationState.animateChanges(),E.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(w)}),E.current=!1))}),p}function $(t){if(t)return!1!==t.options.allowProjection?t.projection:$(t.parent)}function _({preloadedFeatures:e,createVisualElement:r,useRender:a,useVisualState:i,Component:s}){function u(e,u){let l;const c={...o(D),...e,layoutId:z(e)},{isStatic:d}=c,f=F(e),m=i(e,d);if(!d&&v){o(k).strict;const t=function(t){const{drag:n,layout:e}=y;if(!n&&!e)return{};const o={...n,...e};return{MeasureLayout:n?.isEnabled(t)||e?.isEnabled(t)?o.MeasureLayout:void 0,ProjectionNode:o.ProjectionNode}}(c);l=t.MeasureLayout,f.visualElement=U(s,m,c,r,t.ProjectionNode)}return t(R.Provider,{value:f,children:[l&&f.visualElement?n(l,{visualElement:f.visualElement,...c}):null,a(s,e,q(m,f.visualElement,u),m,d,f.visualElement)]})}e&&function(t){for(const n in t)y[n]={...y[n],...t[n]}}(e),u.displayName=`motion.${"string"==typeof s?s:`create(${s.displayName??s.name??""})`}`;const l=c(u);return l[N]=s,l}function z({layoutId:t}){const n=o(w).id;return n&&void 0!==t?n+"-"+t:t}const X=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Y(t,n,e){for(const o in n)E(n[o])||j(o,e)||(t[o]=n[o])}function G(t,n){const e={};return Y(e,t.style||{},t),Object.assign(e,function({transformTemplate:t},n){return r(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return C(e,n,t),Object.assign({},e.vars,e.style)},[n])}(t,n)),e}function J(t,n){const e={},o=G(t,n);return t.drag&&!1!==t.dragListener&&(e.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(e.tabIndex=0),e.style=o,e}const K=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}});function Q(t,n,e,o){const a=r(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return P(e,n,V(o),t.transformTemplate,t.style),{...e.attrs,style:{...e.style}}},[n]);if(t.style){const n={};Y(n,t.style,t),a.style={...n,...a.style}}return a}const Z=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tt(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Z.has(t)}let nt=t=>!tt(t);try{"function"==typeof(et=require("@emotion/is-prop-valid").default)&&(nt=t=>t.startsWith("on")?!tt(t):et(t))}catch{}var et;function ot(t=!1){return(n,e,o,{latestValues:a},i)=>{const s=(A(n)?Q:J)(e,a,i,n),u=function(t,n,e){const o={};for(const r in t)"values"===r&&"object"==typeof t.values||(nt(r)||!0===e&&tt(r)||!n&&!tt(r)||t.draggable&&r.startsWith("onDrag"))&&(o[r]=t[r]);return o}(e,"string"==typeof n,t),l=n!==d?{...u,...s,ref:o}:{},{children:c}=e,m=r(()=>E(c)?c.get():c,[c]);return f(n,{...l,children:m})}}const rt=t=>(n,e)=>{const r=o(R),a=o(h),i=()=>function({scrapeMotionValuesFromProps:t,createRenderState:n},e,o,r){return{latestValues:at(e,o,r,t),renderState:n()}}(t,n,r,a);return e?i():function(t){const n=u(null);return null===n.current&&(n.current=t()),n.current}(i)};function at(t,n,e,o){const r={},a=o(t,{});for(const t in a)r[t]=T(a[t]);let{initial:i,animate:s}=t;const u=m(t),l=L(t);n&&l&&!u&&!1!==t.inherit&&(void 0===i&&(i=n.initial),void 0===s&&(s=n.animate));let c=!!e&&!1===e.initial;c=c||!1===i;const d=c?s:i;if(d&&"boolean"!=typeof d&&!W(d)){const n=Array.isArray(d)?d:[d];for(let e=0;e<n.length;e++){const o=x(t,n[e]);if(o){const{transitionEnd:t,transition:n,...e}=o;for(const t in e){let n=e[t];if(Array.isArray(n)){n=n[c?n.length-1:0]}null!==n&&(r[t]=n)}for(const n in t)r[n]=t[n]}}}return r}const it={useVisualState:rt({scrapeMotionValuesFromProps:I,createRenderState:X})},st={useVisualState:rt({scrapeMotionValuesFromProps:O,createRenderState:K})};function ut(t,n){return function(e,{forwardMotionProps:o}={forwardMotionProps:!1}){return _({...A(e)?st:it,preloadedFeatures:t,useRender:ot(o),createVisualElement:n,Component:e})}}const lt=ut()("div");export{lt as MotionDiv};
